1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolderCcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity(androidx.appcompat.app.AppCompatActivityandroid.app.ServiceQcom.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListener6com.google.firebase.messaging.FirebaseMessagingServiceandroid.app.Application                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       