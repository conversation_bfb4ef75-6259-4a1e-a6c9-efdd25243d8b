Capp/src/main/java/com/example/standardtemplate/Models/TicketDone.ktfapp/src/main/java/com/example/standardtemplate/Activities/NewTickets/Interface/CreateDialogListener.kt[app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Dialog/CreateDialog.kt]app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketListActivity.ktFapp/src/main/java/com/example/standardtemplate/Models/LoginResponse.ktVapp/src/main/java/com/example/standardtemplate/Activities/Register/RegisterActivity.ktJapp/src/main/java/com/example/standardtemplate/Models/AcceptedTicketDto.ktOapp/src/main/java/com/example/standardtemplate/Libraries/Notificationservice.ktiapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketAdapter.ktFapp/src/main/java/com/example/standardtemplate/Models/NewTicketInfo.ktLapp/src/main/java/com/example/standardtemplate/Libraries/TicketMonitoring.ktNapp/src/main/java/com/example/standardtemplate/Models/NewTicketListResponse.ktBapp/src/main/java/com/example/standardtemplate/Models/LoginInfo.kt`app/src/main/java/com/example/standardtemplate/Activities/NewTickets/NewTicketDetailsActivity.ktDapp/src/main/java/com/example/standardtemplate/Models/UserDetails.ktEapp/src/main/java/com/example/standardtemplate/Models/RegisterInfo.ktEapp/src/main/java/com/example/standardtemplate/Libraries/ApiClient.ktXapp/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/BaseActivity.ktXapp/src/main/java/com/example/standardtemplate/Activities/Login_Setting/LoginActivity.ktHapp/src/main/java/com/example/standardtemplate/Models/FcmTokenRequest.kt`app/src/main/java/com/example/standardtemplate/Activities/NewTickets/Adapter/NewTicketAdapter.ktTapp/src/main/java/com/example/standardtemplate/Libraries/Managers/UserInfoManager.ktiapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketDetailsActivity.ktfapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/AcceptedTicketListActivity.ktWapp/src/main/java/com/example/standardtemplate/Activities/Login_Setting/ReturnResult.ktGapp/src/main/java/com/example/standardtemplate/Dialogs/MessageDialog.ktLapp/src/main/java/com/example/standardtemplate/Dialogs/ConfirmationDialog.ktIapp/src/main/java/com/example/standardtemplate/Models/RegisterResponse.ktWapp/src/main/java/com/example/standardtemplate/Libraries/StandardFunction/SharedPref.kt\app/src/main/java/com/example/standardtemplate/Libraries/LanguageSetting/StandardTemplate.kteapp/src/main/java/com/example/standardtemplate/Activities/AcceptedTicket/Adapter/AcceptedTicketDto.ktNapp/src/main/java/com/example/standardtemplate/Libraries/NotificationHelper.kt_app/src/main/java/com/example/standardtemplate/Activities/Login_Setting/Dialog/SettingDialog.ktTapp/src/main/java/com/example/standardtemplate/Libraries/FirebaseMessagingService.ktHapp/src/main/java/com/example/standardtemplate/Libraries/ApiInterface.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               