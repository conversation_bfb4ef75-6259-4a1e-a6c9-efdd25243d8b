#Fri Aug 01 10:57:04 MYT 2025
com.example.standardtemplate.app-main-6\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\application_icon.png
com.example.standardtemplate.app-main-6\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\dialog_background.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_back.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_close.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_background.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_launcher_foreground.xml
com.example.standardtemplate.app-main-6\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\ic_noti_background.xml
com.example.standardtemplate.app-main-6\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\drawable\\input_field_background.xml
com.example.standardtemplate.app-main-6\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_accepted_ticket_details.xml
com.example.standardtemplate.app-main-6\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_accepted_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_login.xml
com.example.standardtemplate.app-main-6\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_new_ticket_details.xml
com.example.standardtemplate.app-main-6\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_new_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\activity_register.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_confirmation.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_create.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_message.xml
com.example.standardtemplate.app-main-6\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\dialog_setting.xml
com.example.standardtemplate.app-main-6\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\item_accepted_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\recycle_view_new_ticket_list.xml
com.example.standardtemplate.app-main-6\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\layout\\spinner_item.xml
com.example.standardtemplate.app-main-6\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher.xml
com.example.standardtemplate.app-main-6\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-anydpi-v26\\ic_launcher_round.xml
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-hdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-mdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_foreground.webp
com.example.standardtemplate.app-main-6\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\mipmap-xxxhdpi-v4\\ic_launcher_round.webp
com.example.standardtemplate.app-main-6\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\backup_rules.xml
com.example.standardtemplate.app-main-6\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\data_extraction_rules.xml
com.example.standardtemplate.app-main-6\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\packaged_res\\debug\\packageDebugResources\\xml\\locales_config.xml
