  Manifest android  R android  
permission android.Manifest  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  FOREGROUND_SERVICE android.Manifest.permission  POST_NOTIFICATIONS android.Manifest.permission  color 	android.R  transparent android.R.color  SuppressLint android.annotation  Activity android.app  	ApiClient android.app  ApiInterface android.app  Application android.app  Build android.app  CHANNEL_ALERTS android.app  CHANNEL_SERVICE android.app  Context android.app  DEFAULT_POLL_INTERVAL_SEC android.app  Date android.app  Dialog android.app  	Exception android.app  	Executors android.app  Intent android.app  Locale android.app  Log android.app  
LoginActivity android.app  NewTicketListActivity android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  
PendingIntent android.app  R android.app  SERVICE_NOTIFICATION_ID android.app  START_STICKY android.app  Service android.app  
SharedPref android.app  SimpleDateFormat android.app  System android.app  TicketMonitoring android.app  TimeUnit android.app  Toast android.app  UserInfoManager android.app  applicationContext android.app  apply android.app  	getString android.app  isBlank android.app  isEmpty android.app  java android.app  
lastDetect android.app  	lastVisit android.app  listOf android.app  showTicketNotification android.app  toString android.app  AcceptedTicketAdapter android.app.Activity  AcceptedTicketDto android.app.Activity  AcceptedTicketListActivity android.app.Activity  ActivityCompat android.app.Activity  AdapterView android.app.Activity  	ApiClient android.app.Activity  ApiInterface android.app.Activity  AppCompatDelegate android.app.Activity  ArrayAdapter android.app.Activity  Boolean android.app.Activity  Build android.app.Activity  Bundle android.app.Activity  Button android.app.Activity  Call android.app.Activity  Callback android.app.Activity  
Configuration android.app.Activity  Context android.app.Activity  
ContextCompat android.app.Activity  CreateDialog android.app.Activity  Date android.app.Activity  EditText android.app.Activity  FcmTokenRequest android.app.Activity  FirebaseMessaging android.app.Activity  GridLayoutManager android.app.Activity  Handler android.app.Activity  
HandlerThread android.app.Activity  Int android.app.Activity  Intent android.app.Activity  List android.app.Activity  Locale android.app.Activity  LocaleListCompat android.app.Activity  Log android.app.Activity  
LoginActivity android.app.Activity  	LoginInfo android.app.Activity  
LoginResponse android.app.Activity  Long android.app.Activity  Manifest android.app.Activity  Map android.app.Activity  MutableList android.app.Activity  NewTicketAdapter android.app.Activity  
NewTicketInfo android.app.Activity  NewTicketListActivity android.app.Activity  NewTicketListResponse android.app.Activity  OnBackPressedCallback android.app.Activity  PackageManager android.app.Activity  PowerManager android.app.Activity  R android.app.Activity  RecyclerView android.app.Activity  RegisterActivity android.app.Activity  RegisterInfo android.app.Activity  RegisterResponse android.app.Activity  Response android.app.Activity  Settings android.app.Activity  
SharedPref android.app.Activity  SimpleDateFormat android.app.Activity  Spinner android.app.Activity  StandardTemplate android.app.Activity  String android.app.Activity  SuppressLint android.app.Activity  TextView android.app.Activity  	Throwable android.app.Activity  
TicketDone android.app.Activity  TicketMonitoring android.app.Activity  Toast android.app.Activity  Unit android.app.Activity  Uri android.app.Activity  UserDetails android.app.Activity  UserInfoManager android.app.Activity  View android.app.Activity  
ViewCompat android.app.Activity  Void android.app.Activity  WindowInsetsCompat android.app.Activity  acceptTicket android.app.Activity  acceptedTicketAdapter android.app.Activity  acceptedTicketList android.app.Activity  applicationContext android.app.Activity  apply android.app.Activity  attachBaseContext android.app.Activity  call_new_ticket_list android.app.Activity  changeLanguage android.app.Activity  checkPermission android.app.Activity  dLocale android.app.Activity  enableEdgeToEdge android.app.Activity  exitProcess android.app.Activity  fetchAcceptedTickets android.app.Activity  fetchNewTicketList android.app.Activity  findViewById android.app.Activity  finish android.app.Activity  finishAffinity android.app.Activity  getAcceptedTicketList android.app.Activity  	getString android.app.Activity  getSystemService android.app.Activity  getUserData android.app.Activity  handler android.app.Activity  isEmpty android.app.Activity  
isNotEmpty android.app.Activity  
isNullOrEmpty android.app.Activity  java android.app.Activity  	lastVisit android.app.Activity  let android.app.Activity  listOf android.app.Activity  login android.app.Activity  mapOf android.app.Activity  maybeRequestBatteryWhitelist android.app.Activity  
mutableListOf android.app.Activity  newTicketAdapter android.app.Activity  onBackPressedDispatcher android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  onPause android.app.Activity  onResume android.app.Activity  recreate android.app.Activity  register android.app.Activity  registerFCMToken android.app.Activity  	retrofit2 android.app.Activity  sendFcmToken android.app.Activity  setContentView android.app.Activity  
sharedPref android.app.Activity  showConfirmDialog android.app.Activity  showMessageDialog android.app.Activity  showSettingDialog android.app.Activity  solvedTicket android.app.Activity  
startActivity android.app.Activity  startMonitoringService android.app.Activity  to android.app.Activity  toInt android.app.Activity  toString android.app.Activity  toTypedArray android.app.Activity  trim android.app.Activity  username android.app.Activity  AppCompatDelegate android.app.Application  LocaleListCompat android.app.Application  Log android.app.Application  
SharedPref android.app.Application  onCreate android.app.Application  	setLocale android.app.Application  dismiss android.app.Dialog  	getWINDOW android.app.Dialog  	getWindow android.app.Dialog  requestWindowFeature android.app.Dialog  setContentView android.app.Dialog  	setWindow android.app.Dialog  show android.app.Dialog  window android.app.Dialog  apply android.app.NotificationChannel  enableVibration android.app.NotificationChannel  getAPPLY android.app.NotificationChannel  getApply android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_HIGH android.app.NotificationManager  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  createNotificationChannels android.app.NotificationManager  notify android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  getActivity android.app.PendingIntent  	ApiClient android.app.Service  ApiInterface android.app.Service  Build android.app.Service  CHANNEL_ALERTS android.app.Service  CHANNEL_SERVICE android.app.Service  Call android.app.Service  Callback android.app.Service  Context android.app.Service  DEFAULT_POLL_INTERVAL_SEC android.app.Service  Date android.app.Service  	Exception android.app.Service  	Executors android.app.Service  IBinder android.app.Service  Int android.app.Service  Intent android.app.Service  Locale android.app.Service  Log android.app.Service  
LoginActivity android.app.Service  NewTicketListActivity android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  NotificationManagerCompat android.app.Service  
PendingIntent android.app.Service  R android.app.Service  
RemoteMessage android.app.Service  Response android.app.Service  SERVICE_NOTIFICATION_ID android.app.Service  START_NOT_STICKY android.app.Service  START_STICKY android.app.Service  ScheduledExecutorService android.app.Service  
SharedPref android.app.Service  SimpleDateFormat android.app.Service  String android.app.Service  System android.app.Service  	Throwable android.app.Service  TicketMonitoring android.app.Service  TimeUnit android.app.Service  Toast android.app.Service  UserInfoManager android.app.Service  applicationContext android.app.Service  apply android.app.Service  buildServiceNotification android.app.Service  checkForNewTickets android.app.Service  createForegroundNotification android.app.Service  createNotification android.app.Service  createNotificationChannel android.app.Service  createNotificationChannels android.app.Service  	getString android.app.Service  getSystemService android.app.Service  isBlank android.app.Service  isEmpty android.app.Service  java android.app.Service  
lastDetect android.app.Service  	lastVisit android.app.Service  listOf android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  
onNewToken android.app.Service  
onTaskRemoved android.app.Service  sendNotification android.app.Service  showTicketNotification android.app.Service  startForeground android.app.Service  startPolling android.app.Service  stopSelf android.app.Service  ticketTapIntent android.app.Service  toIntOrNull android.app.Service  toString android.app.Service  
ComponentName android.content  
ContentValues android.content  Context android.content  Intent android.content  SharedPreferences android.content  TAG android.content.ContentValues  AcceptedTicketAdapter android.content.Context  AcceptedTicketDto android.content.Context  AcceptedTicketListActivity android.content.Context  ActivityCompat android.content.Context  AdapterView android.content.Context  	ApiClient android.content.Context  ApiInterface android.content.Context  AppCompatDelegate android.content.Context  ArrayAdapter android.content.Context  Boolean android.content.Context  Build android.content.Context  Bundle android.content.Context  Button android.content.Context  CHANNEL_ALERTS android.content.Context  CHANNEL_SERVICE android.content.Context  Call android.content.Context  Callback android.content.Context  
Configuration android.content.Context  Context android.content.Context  
ContextCompat android.content.Context  CreateDialog android.content.Context  DEFAULT_POLL_INTERVAL_SEC android.content.Context  Date android.content.Context  EditText android.content.Context  	Exception android.content.Context  	Executors android.content.Context  FcmTokenRequest android.content.Context  FirebaseMessaging android.content.Context  GridLayoutManager android.content.Context  Handler android.content.Context  
HandlerThread android.content.Context  IBinder android.content.Context  Int android.content.Context  Intent android.content.Context  List android.content.Context  Locale android.content.Context  LocaleListCompat android.content.Context  Log android.content.Context  
LoginActivity android.content.Context  	LoginInfo android.content.Context  
LoginResponse android.content.Context  Long android.content.Context  MODE_PRIVATE android.content.Context  Manifest android.content.Context  Map android.content.Context  MutableList android.content.Context  NOTIFICATION_SERVICE android.content.Context  NewTicketAdapter android.content.Context  
NewTicketInfo android.content.Context  NewTicketListActivity android.content.Context  NewTicketListResponse android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  NotificationManagerCompat android.content.Context  OnBackPressedCallback android.content.Context  
POWER_SERVICE android.content.Context  PackageManager android.content.Context  
PendingIntent android.content.Context  PowerManager android.content.Context  R android.content.Context  RecyclerView android.content.Context  RegisterActivity android.content.Context  RegisterInfo android.content.Context  RegisterResponse android.content.Context  
RemoteMessage android.content.Context  Response android.content.Context  SERVICE_NOTIFICATION_ID android.content.Context  START_NOT_STICKY android.content.Context  START_STICKY android.content.Context  ScheduledExecutorService android.content.Context  Settings android.content.Context  
SharedPref android.content.Context  SimpleDateFormat android.content.Context  Spinner android.content.Context  StandardTemplate android.content.Context  String android.content.Context  SuppressLint android.content.Context  System android.content.Context  TextView android.content.Context  	Throwable android.content.Context  
TicketDone android.content.Context  TicketMonitoring android.content.Context  TimeUnit android.content.Context  Toast android.content.Context  Unit android.content.Context  Uri android.content.Context  UserDetails android.content.Context  UserInfoManager android.content.Context  View android.content.Context  
ViewCompat android.content.Context  Void android.content.Context  WindowInsetsCompat android.content.Context  acceptTicket android.content.Context  acceptedTicketAdapter android.content.Context  acceptedTicketList android.content.Context  applicationContext android.content.Context  apply android.content.Context  attachBaseContext android.content.Context  buildServiceNotification android.content.Context  call_new_ticket_list android.content.Context  changeLanguage android.content.Context  checkForNewTickets android.content.Context  checkPermission android.content.Context  createConfigurationContext android.content.Context  createForegroundNotification android.content.Context  createNotification android.content.Context  createNotificationChannel android.content.Context  createNotificationChannels android.content.Context  dLocale android.content.Context  enableEdgeToEdge android.content.Context  exitProcess android.content.Context  fetchAcceptedTickets android.content.Context  fetchNewTicketList android.content.Context  findViewById android.content.Context  finish android.content.Context  finishAffinity android.content.Context  getAcceptedTicketList android.content.Context  getRESOURCES android.content.Context  getResources android.content.Context  getSharedPreferences android.content.Context  	getString android.content.Context  getSystemService android.content.Context  getUserData android.content.Context  handler android.content.Context  isBlank android.content.Context  isEmpty android.content.Context  
isNotEmpty android.content.Context  
isNullOrEmpty android.content.Context  java android.content.Context  
lastDetect android.content.Context  	lastVisit android.content.Context  let android.content.Context  listOf android.content.Context  login android.content.Context  mapOf android.content.Context  maybeRequestBatteryWhitelist android.content.Context  
mutableListOf android.content.Context  newTicketAdapter android.content.Context  onBackPressedDispatcher android.content.Context  onCreate android.content.Context  	onDestroy android.content.Context  
onNewToken android.content.Context  onPause android.content.Context  onResume android.content.Context  
onTaskRemoved android.content.Context  recreate android.content.Context  register android.content.Context  registerFCMToken android.content.Context  	resources android.content.Context  	retrofit2 android.content.Context  sendFcmToken android.content.Context  sendNotification android.content.Context  setContentView android.content.Context  	setLocale android.content.Context  setResources android.content.Context  
sharedPref android.content.Context  showConfirmDialog android.content.Context  showMessageDialog android.content.Context  showSettingDialog android.content.Context  showTicketNotification android.content.Context  solvedTicket android.content.Context  
startActivity android.content.Context  startForeground android.content.Context  startForegroundService android.content.Context  startMonitoringService android.content.Context  startPolling android.content.Context  startService android.content.Context  stopSelf android.content.Context  stopService android.content.Context  ticketTapIntent android.content.Context  to android.content.Context  toInt android.content.Context  toIntOrNull android.content.Context  toString android.content.Context  toTypedArray android.content.Context  trim android.content.Context  username android.content.Context  AcceptedTicketAdapter android.content.ContextWrapper  AcceptedTicketDto android.content.ContextWrapper  AcceptedTicketListActivity android.content.ContextWrapper  ActivityCompat android.content.ContextWrapper  AdapterView android.content.ContextWrapper  	ApiClient android.content.ContextWrapper  ApiInterface android.content.ContextWrapper  AppCompatDelegate android.content.ContextWrapper  ArrayAdapter android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  Bundle android.content.ContextWrapper  Button android.content.ContextWrapper  CHANNEL_ALERTS android.content.ContextWrapper  CHANNEL_SERVICE android.content.ContextWrapper  Call android.content.ContextWrapper  Callback android.content.ContextWrapper  
Configuration android.content.ContextWrapper  Context android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  CreateDialog android.content.ContextWrapper  DEFAULT_POLL_INTERVAL_SEC android.content.ContextWrapper  Date android.content.ContextWrapper  EditText android.content.ContextWrapper  	Exception android.content.ContextWrapper  	Executors android.content.ContextWrapper  FcmTokenRequest android.content.ContextWrapper  FirebaseMessaging android.content.ContextWrapper  GridLayoutManager android.content.ContextWrapper  Handler android.content.ContextWrapper  
HandlerThread android.content.ContextWrapper  IBinder android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  List android.content.ContextWrapper  Locale android.content.ContextWrapper  LocaleListCompat android.content.ContextWrapper  Log android.content.ContextWrapper  
LoginActivity android.content.ContextWrapper  	LoginInfo android.content.ContextWrapper  
LoginResponse android.content.ContextWrapper  Long android.content.ContextWrapper  Manifest android.content.ContextWrapper  Map android.content.ContextWrapper  MutableList android.content.ContextWrapper  NewTicketAdapter android.content.ContextWrapper  
NewTicketInfo android.content.ContextWrapper  NewTicketListActivity android.content.ContextWrapper  NewTicketListResponse android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  NotificationManagerCompat android.content.ContextWrapper  OnBackPressedCallback android.content.ContextWrapper  PackageManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  PowerManager android.content.ContextWrapper  R android.content.ContextWrapper  RecyclerView android.content.ContextWrapper  RegisterActivity android.content.ContextWrapper  RegisterInfo android.content.ContextWrapper  RegisterResponse android.content.ContextWrapper  
RemoteMessage android.content.ContextWrapper  Response android.content.ContextWrapper  SERVICE_NOTIFICATION_ID android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  START_STICKY android.content.ContextWrapper  ScheduledExecutorService android.content.ContextWrapper  Settings android.content.ContextWrapper  
SharedPref android.content.ContextWrapper  SimpleDateFormat android.content.ContextWrapper  Spinner android.content.ContextWrapper  StandardTemplate android.content.ContextWrapper  String android.content.ContextWrapper  SuppressLint android.content.ContextWrapper  System android.content.ContextWrapper  TextView android.content.ContextWrapper  	Throwable android.content.ContextWrapper  
TicketDone android.content.ContextWrapper  TicketMonitoring android.content.ContextWrapper  TimeUnit android.content.ContextWrapper  Toast android.content.ContextWrapper  Unit android.content.ContextWrapper  Uri android.content.ContextWrapper  UserDetails android.content.ContextWrapper  UserInfoManager android.content.ContextWrapper  View android.content.ContextWrapper  
ViewCompat android.content.ContextWrapper  Void android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  acceptTicket android.content.ContextWrapper  acceptedTicketAdapter android.content.ContextWrapper  acceptedTicketList android.content.ContextWrapper  applicationContext android.content.ContextWrapper  apply android.content.ContextWrapper  attachBaseContext android.content.ContextWrapper  buildServiceNotification android.content.ContextWrapper  call_new_ticket_list android.content.ContextWrapper  changeLanguage android.content.ContextWrapper  checkForNewTickets android.content.ContextWrapper  checkPermission android.content.ContextWrapper  createForegroundNotification android.content.ContextWrapper  createNotification android.content.ContextWrapper  createNotificationChannel android.content.ContextWrapper  createNotificationChannels android.content.ContextWrapper  dLocale android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  exitProcess android.content.ContextWrapper  fetchAcceptedTickets android.content.ContextWrapper  fetchNewTicketList android.content.ContextWrapper  findViewById android.content.ContextWrapper  finish android.content.ContextWrapper  finishAffinity android.content.ContextWrapper  getAcceptedTicketList android.content.ContextWrapper  	getString android.content.ContextWrapper  getSystemService android.content.ContextWrapper  getUserData android.content.ContextWrapper  handler android.content.ContextWrapper  isBlank android.content.ContextWrapper  isEmpty android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
isNullOrEmpty android.content.ContextWrapper  java android.content.ContextWrapper  
lastDetect android.content.ContextWrapper  	lastVisit android.content.ContextWrapper  let android.content.ContextWrapper  listOf android.content.ContextWrapper  login android.content.ContextWrapper  mapOf android.content.ContextWrapper  maybeRequestBatteryWhitelist android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  newTicketAdapter android.content.ContextWrapper  onBackPressedDispatcher android.content.ContextWrapper  onCreate android.content.ContextWrapper  	onDestroy android.content.ContextWrapper  
onNewToken android.content.ContextWrapper  onPause android.content.ContextWrapper  onResume android.content.ContextWrapper  
onTaskRemoved android.content.ContextWrapper  recreate android.content.ContextWrapper  register android.content.ContextWrapper  registerFCMToken android.content.ContextWrapper  	retrofit2 android.content.ContextWrapper  sendFcmToken android.content.ContextWrapper  sendNotification android.content.ContextWrapper  setContentView android.content.ContextWrapper  	setLocale android.content.ContextWrapper  
sharedPref android.content.ContextWrapper  showConfirmDialog android.content.ContextWrapper  showMessageDialog android.content.ContextWrapper  showSettingDialog android.content.ContextWrapper  showTicketNotification android.content.ContextWrapper  solvedTicket android.content.ContextWrapper  
startActivity android.content.ContextWrapper  startForeground android.content.ContextWrapper  startMonitoringService android.content.ContextWrapper  startPolling android.content.ContextWrapper  stopSelf android.content.ContextWrapper  ticketTapIntent android.content.ContextWrapper  to android.content.ContextWrapper  toInt android.content.ContextWrapper  toIntOrNull android.content.ContextWrapper  toString android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  trim android.content.ContextWrapper  username android.content.ContextWrapper  FLAG_ACTIVITY_CLEAR_TASK android.content.Intent  FLAG_ACTIVITY_CLEAR_TOP android.content.Intent  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  Uri android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  data android.content.Intent  flags android.content.Intent  getAPPLY android.content.Intent  getApply android.content.Intent  getDATA android.content.Intent  getData android.content.Intent  getFLAGS android.content.Intent  getFlags android.content.Intent  getIntExtra android.content.Intent  getStringExtra android.content.Intent  putExtra android.content.Intent  setData android.content.Intent  setFlags android.content.Intent  
setPackage android.content.Intent  Editor !android.content.SharedPreferences  edit !android.content.SharedPreferences  	getString !android.content.SharedPreferences  apply (android.content.SharedPreferences.Editor  clear (android.content.SharedPreferences.Editor  commit (android.content.SharedPreferences.Editor  	putString (android.content.SharedPreferences.Editor  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  
Configuration android.content.res  	setLocale !android.content.res.Configuration  
configuration android.content.res.Resources  getCONFIGURATION android.content.res.Resources  getConfiguration android.content.res.Resources  setConfiguration android.content.res.Resources  Uri android.net  parse android.net.Uri  Build 
android.os  Bundle 
android.os  Handler 
android.os  
HandlerThread 
android.os  IBinder 
android.os  Looper 
android.os  PowerManager 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  M android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  UPSIDE_DOWN_CAKE android.os.Build.VERSION_CODES  post android.os.Handler  postDelayed android.os.Handler  removeCallbacksAndMessages android.os.Handler  apply android.os.HandlerThread  getAPPLY android.os.HandlerThread  getApply android.os.HandlerThread  	getLOOPER android.os.HandlerThread  	getLooper android.os.HandlerThread  looper android.os.HandlerThread  
quitSafely android.os.HandlerThread  	setLooper android.os.HandlerThread  start android.os.HandlerThread  quit android.os.Looper  isIgnoringBatteryOptimizations android.os.PowerManager  Settings android.provider  +ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS android.provider.Settings  Editable android.text  equals android.text.Editable  toString android.text.Editable  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  LayoutInflater android.view  View android.view  	ViewGroup android.view  Window android.view  AcceptedTicketAdapter  android.view.ContextThemeWrapper  AcceptedTicketDto  android.view.ContextThemeWrapper  AcceptedTicketListActivity  android.view.ContextThemeWrapper  ActivityCompat  android.view.ContextThemeWrapper  AdapterView  android.view.ContextThemeWrapper  	ApiClient  android.view.ContextThemeWrapper  ApiInterface  android.view.ContextThemeWrapper  AppCompatDelegate  android.view.ContextThemeWrapper  ArrayAdapter  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Build  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  Call  android.view.ContextThemeWrapper  Callback  android.view.ContextThemeWrapper  
Configuration  android.view.ContextThemeWrapper  Context  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  CreateDialog  android.view.ContextThemeWrapper  Date  android.view.ContextThemeWrapper  EditText  android.view.ContextThemeWrapper  FcmTokenRequest  android.view.ContextThemeWrapper  FirebaseMessaging  android.view.ContextThemeWrapper  GridLayoutManager  android.view.ContextThemeWrapper  Handler  android.view.ContextThemeWrapper  
HandlerThread  android.view.ContextThemeWrapper  Int  android.view.ContextThemeWrapper  Intent  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  Locale  android.view.ContextThemeWrapper  LocaleListCompat  android.view.ContextThemeWrapper  Log  android.view.ContextThemeWrapper  
LoginActivity  android.view.ContextThemeWrapper  	LoginInfo  android.view.ContextThemeWrapper  
LoginResponse  android.view.ContextThemeWrapper  Long  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  Map  android.view.ContextThemeWrapper  MutableList  android.view.ContextThemeWrapper  NewTicketAdapter  android.view.ContextThemeWrapper  
NewTicketInfo  android.view.ContextThemeWrapper  NewTicketListActivity  android.view.ContextThemeWrapper  NewTicketListResponse  android.view.ContextThemeWrapper  OnBackPressedCallback  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  PowerManager  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  RecyclerView  android.view.ContextThemeWrapper  RegisterActivity  android.view.ContextThemeWrapper  RegisterInfo  android.view.ContextThemeWrapper  RegisterResponse  android.view.ContextThemeWrapper  Response  android.view.ContextThemeWrapper  Settings  android.view.ContextThemeWrapper  
SharedPref  android.view.ContextThemeWrapper  SimpleDateFormat  android.view.ContextThemeWrapper  Spinner  android.view.ContextThemeWrapper  StandardTemplate  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  SuppressLint  android.view.ContextThemeWrapper  TextView  android.view.ContextThemeWrapper  	Throwable  android.view.ContextThemeWrapper  
TicketDone  android.view.ContextThemeWrapper  TicketMonitoring  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  Unit  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  UserDetails  android.view.ContextThemeWrapper  UserInfoManager  android.view.ContextThemeWrapper  View  android.view.ContextThemeWrapper  
ViewCompat  android.view.ContextThemeWrapper  Void  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  acceptTicket  android.view.ContextThemeWrapper  acceptedTicketAdapter  android.view.ContextThemeWrapper  acceptedTicketList  android.view.ContextThemeWrapper  applicationContext  android.view.ContextThemeWrapper  apply  android.view.ContextThemeWrapper  attachBaseContext  android.view.ContextThemeWrapper  call_new_ticket_list  android.view.ContextThemeWrapper  changeLanguage  android.view.ContextThemeWrapper  checkPermission  android.view.ContextThemeWrapper  dLocale  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  exitProcess  android.view.ContextThemeWrapper  fetchAcceptedTickets  android.view.ContextThemeWrapper  fetchNewTicketList  android.view.ContextThemeWrapper  findViewById  android.view.ContextThemeWrapper  finish  android.view.ContextThemeWrapper  finishAffinity  android.view.ContextThemeWrapper  getAcceptedTicketList  android.view.ContextThemeWrapper  	getString  android.view.ContextThemeWrapper  getSystemService  android.view.ContextThemeWrapper  getUserData  android.view.ContextThemeWrapper  handler  android.view.ContextThemeWrapper  isEmpty  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
isNullOrEmpty  android.view.ContextThemeWrapper  java  android.view.ContextThemeWrapper  	lastVisit  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  listOf  android.view.ContextThemeWrapper  login  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  maybeRequestBatteryWhitelist  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  newTicketAdapter  android.view.ContextThemeWrapper  onBackPressedDispatcher  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  	onDestroy  android.view.ContextThemeWrapper  onPause  android.view.ContextThemeWrapper  onResume  android.view.ContextThemeWrapper  recreate  android.view.ContextThemeWrapper  register  android.view.ContextThemeWrapper  registerFCMToken  android.view.ContextThemeWrapper  	retrofit2  android.view.ContextThemeWrapper  sendFcmToken  android.view.ContextThemeWrapper  setContentView  android.view.ContextThemeWrapper  
sharedPref  android.view.ContextThemeWrapper  showConfirmDialog  android.view.ContextThemeWrapper  showMessageDialog  android.view.ContextThemeWrapper  showSettingDialog  android.view.ContextThemeWrapper  solvedTicket  android.view.ContextThemeWrapper  
startActivity  android.view.ContextThemeWrapper  startMonitoringService  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  toInt  android.view.ContextThemeWrapper  toString  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  trim  android.view.ContextThemeWrapper  username  android.view.ContextThemeWrapper  from android.view.LayoutInflater  inflate android.view.LayoutInflater  context android.view.View  findViewById android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  
setContext android.view.View  setOnClickListener android.view.View  
setPadding android.view.View  setSelection android.view.View  setText android.view.View  <SAM-CONSTRUCTOR> !android.view.View.OnClickListener  context android.view.ViewGroup  
getCONTEXT android.view.ViewGroup  
getContext android.view.ViewGroup  
setContext android.view.ViewGroup  setOnClickListener android.view.ViewGroup  setSelection android.view.ViewGroup  FEATURE_NO_TITLE android.view.Window  setBackgroundDrawableResource android.view.Window  AdapterView android.widget  ArrayAdapter android.widget  Button android.widget  EditText android.widget  	ImageView android.widget  Spinner android.widget  SpinnerAdapter android.widget  TextView android.widget  Toast android.widget  setSelection android.widget.AbsSpinner  OnItemSelectedListener android.widget.AdapterView  setSelection android.widget.AdapterView  setOnClickListener android.widget.Button  error android.widget.EditText  getERROR android.widget.EditText  getError android.widget.EditText  getISFocusable android.widget.EditText  getIsFocusable android.widget.EditText  getTEXT android.widget.EditText  getText android.widget.EditText  isFocusable android.widget.EditText  setError android.widget.EditText  setFocusable android.widget.EditText  setText android.widget.EditText  text android.widget.EditText  setOnClickListener android.widget.FrameLayout  setOnClickListener android.widget.ImageView  adapter android.widget.Spinner  
getADAPTER android.widget.Spinner  
getAdapter android.widget.Spinner  getONItemSelectedListener android.widget.Spinner  getOnItemSelectedListener android.widget.Spinner  onItemSelectedListener android.widget.Spinner  
setAdapter android.widget.Spinner  setOnItemSelectedListener android.widget.Spinner  setSelection android.widget.Spinner  getTEXT android.widget.TextView  getText android.widget.TextView  setOnClickListener android.widget.TextView  setText android.widget.TextView  text android.widget.TextView  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  OnBackPressedCallback androidx.activity  OnBackPressedDispatcher androidx.activity  enableEdgeToEdge androidx.activity  AcceptedTicketAdapter #androidx.activity.ComponentActivity  AcceptedTicketDto #androidx.activity.ComponentActivity  AcceptedTicketListActivity #androidx.activity.ComponentActivity  ActivityCompat #androidx.activity.ComponentActivity  AdapterView #androidx.activity.ComponentActivity  	ApiClient #androidx.activity.ComponentActivity  ApiInterface #androidx.activity.ComponentActivity  AppCompatDelegate #androidx.activity.ComponentActivity  ArrayAdapter #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Build #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  Call #androidx.activity.ComponentActivity  Callback #androidx.activity.ComponentActivity  
Configuration #androidx.activity.ComponentActivity  Context #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  CreateDialog #androidx.activity.ComponentActivity  Date #androidx.activity.ComponentActivity  EditText #androidx.activity.ComponentActivity  FcmTokenRequest #androidx.activity.ComponentActivity  FirebaseMessaging #androidx.activity.ComponentActivity  GridLayoutManager #androidx.activity.ComponentActivity  Handler #androidx.activity.ComponentActivity  
HandlerThread #androidx.activity.ComponentActivity  Int #androidx.activity.ComponentActivity  Intent #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Locale #androidx.activity.ComponentActivity  LocaleListCompat #androidx.activity.ComponentActivity  Log #androidx.activity.ComponentActivity  
LoginActivity #androidx.activity.ComponentActivity  	LoginInfo #androidx.activity.ComponentActivity  
LoginResponse #androidx.activity.ComponentActivity  Long #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  Map #androidx.activity.ComponentActivity  MutableList #androidx.activity.ComponentActivity  NewTicketAdapter #androidx.activity.ComponentActivity  
NewTicketInfo #androidx.activity.ComponentActivity  NewTicketListActivity #androidx.activity.ComponentActivity  NewTicketListResponse #androidx.activity.ComponentActivity  OnBackPressedCallback #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  PowerManager #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  RecyclerView #androidx.activity.ComponentActivity  RegisterActivity #androidx.activity.ComponentActivity  RegisterInfo #androidx.activity.ComponentActivity  RegisterResponse #androidx.activity.ComponentActivity  Response #androidx.activity.ComponentActivity  Settings #androidx.activity.ComponentActivity  
SharedPref #androidx.activity.ComponentActivity  SimpleDateFormat #androidx.activity.ComponentActivity  Spinner #androidx.activity.ComponentActivity  StandardTemplate #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  SuppressLint #androidx.activity.ComponentActivity  TextView #androidx.activity.ComponentActivity  	Throwable #androidx.activity.ComponentActivity  
TicketDone #androidx.activity.ComponentActivity  TicketMonitoring #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  UserDetails #androidx.activity.ComponentActivity  UserInfoManager #androidx.activity.ComponentActivity  View #androidx.activity.ComponentActivity  
ViewCompat #androidx.activity.ComponentActivity  Void #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  acceptTicket #androidx.activity.ComponentActivity  acceptedTicketAdapter #androidx.activity.ComponentActivity  acceptedTicketList #androidx.activity.ComponentActivity  applicationContext #androidx.activity.ComponentActivity  apply #androidx.activity.ComponentActivity  attachBaseContext #androidx.activity.ComponentActivity  call_new_ticket_list #androidx.activity.ComponentActivity  changeLanguage #androidx.activity.ComponentActivity  checkPermission #androidx.activity.ComponentActivity  dLocale #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  exitProcess #androidx.activity.ComponentActivity  fetchAcceptedTickets #androidx.activity.ComponentActivity  fetchNewTicketList #androidx.activity.ComponentActivity  findViewById #androidx.activity.ComponentActivity  finish #androidx.activity.ComponentActivity  finishAffinity #androidx.activity.ComponentActivity  getAcceptedTicketList #androidx.activity.ComponentActivity  	getString #androidx.activity.ComponentActivity  getSystemService #androidx.activity.ComponentActivity  getUserData #androidx.activity.ComponentActivity  handler #androidx.activity.ComponentActivity  isEmpty #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
isNullOrEmpty #androidx.activity.ComponentActivity  java #androidx.activity.ComponentActivity  	lastVisit #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  listOf #androidx.activity.ComponentActivity  login #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  maybeRequestBatteryWhitelist #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  newTicketAdapter #androidx.activity.ComponentActivity  onBackPressedDispatcher #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  onPause #androidx.activity.ComponentActivity  onResume #androidx.activity.ComponentActivity  recreate #androidx.activity.ComponentActivity  register #androidx.activity.ComponentActivity  registerFCMToken #androidx.activity.ComponentActivity  	retrofit2 #androidx.activity.ComponentActivity  sendFcmToken #androidx.activity.ComponentActivity  setContentView #androidx.activity.ComponentActivity  
sharedPref #androidx.activity.ComponentActivity  showConfirmDialog #androidx.activity.ComponentActivity  showMessageDialog #androidx.activity.ComponentActivity  showSettingDialog #androidx.activity.ComponentActivity  solvedTicket #androidx.activity.ComponentActivity  
startActivity #androidx.activity.ComponentActivity  startMonitoringService #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  toInt #androidx.activity.ComponentActivity  toString #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  trim #androidx.activity.ComponentActivity  username #androidx.activity.ComponentActivity  AcceptedTicketAdapter -androidx.activity.ComponentActivity.Companion  AcceptedTicketListActivity -androidx.activity.ComponentActivity.Companion  ActivityCompat -androidx.activity.ComponentActivity.Companion  	ApiClient -androidx.activity.ComponentActivity.Companion  ApiInterface -androidx.activity.ComponentActivity.Companion  AppCompatDelegate -androidx.activity.ComponentActivity.Companion  ArrayAdapter -androidx.activity.ComponentActivity.Companion  Build -androidx.activity.ComponentActivity.Companion  
Configuration -androidx.activity.ComponentActivity.Companion  Context -androidx.activity.ComponentActivity.Companion  
ContextCompat -androidx.activity.ComponentActivity.Companion  CreateDialog -androidx.activity.ComponentActivity.Companion  Date -androidx.activity.ComponentActivity.Companion  FcmTokenRequest -androidx.activity.ComponentActivity.Companion  FirebaseMessaging -androidx.activity.ComponentActivity.Companion  GridLayoutManager -androidx.activity.ComponentActivity.Companion  Handler -androidx.activity.ComponentActivity.Companion  
HandlerThread -androidx.activity.ComponentActivity.Companion  Intent -androidx.activity.ComponentActivity.Companion  Locale -androidx.activity.ComponentActivity.Companion  LocaleListCompat -androidx.activity.ComponentActivity.Companion  Log -androidx.activity.ComponentActivity.Companion  
LoginActivity -androidx.activity.ComponentActivity.Companion  	LoginInfo -androidx.activity.ComponentActivity.Companion  Manifest -androidx.activity.ComponentActivity.Companion  NewTicketAdapter -androidx.activity.ComponentActivity.Companion  
NewTicketInfo -androidx.activity.ComponentActivity.Companion  NewTicketListActivity -androidx.activity.ComponentActivity.Companion  PackageManager -androidx.activity.ComponentActivity.Companion  R -androidx.activity.ComponentActivity.Companion  RegisterActivity -androidx.activity.ComponentActivity.Companion  RegisterInfo -androidx.activity.ComponentActivity.Companion  Settings -androidx.activity.ComponentActivity.Companion  
SharedPref -androidx.activity.ComponentActivity.Companion  SimpleDateFormat -androidx.activity.ComponentActivity.Companion  
TicketDone -androidx.activity.ComponentActivity.Companion  TicketMonitoring -androidx.activity.ComponentActivity.Companion  Toast -androidx.activity.ComponentActivity.Companion  Uri -androidx.activity.ComponentActivity.Companion  UserInfoManager -androidx.activity.ComponentActivity.Companion  
ViewCompat -androidx.activity.ComponentActivity.Companion  WindowInsetsCompat -androidx.activity.ComponentActivity.Companion  acceptedTicketAdapter -androidx.activity.ComponentActivity.Companion  acceptedTicketList -androidx.activity.ComponentActivity.Companion  applicationContext -androidx.activity.ComponentActivity.Companion  apply -androidx.activity.ComponentActivity.Companion  call_new_ticket_list -androidx.activity.ComponentActivity.Companion  changeLanguage -androidx.activity.ComponentActivity.Companion  dLocale -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  exitProcess -androidx.activity.ComponentActivity.Companion  finish -androidx.activity.ComponentActivity.Companion  finishAffinity -androidx.activity.ComponentActivity.Companion  getAPPLY -androidx.activity.ComponentActivity.Companion  getApply -androidx.activity.ComponentActivity.Companion  getEXITProcess -androidx.activity.ComponentActivity.Companion  getExitProcess -androidx.activity.ComponentActivity.Companion  
getISEmpty -androidx.activity.ComponentActivity.Companion  
getISNotEmpty -androidx.activity.ComponentActivity.Companion  getISNullOrEmpty -androidx.activity.ComponentActivity.Companion  
getIsEmpty -androidx.activity.ComponentActivity.Companion  
getIsNotEmpty -androidx.activity.ComponentActivity.Companion  getIsNullOrEmpty -androidx.activity.ComponentActivity.Companion  getLET -androidx.activity.ComponentActivity.Companion  	getLISTOf -androidx.activity.ComponentActivity.Companion  getLet -androidx.activity.ComponentActivity.Companion  	getListOf -androidx.activity.ComponentActivity.Companion  getMAPOf -androidx.activity.ComponentActivity.Companion  getMUTABLEListOf -androidx.activity.ComponentActivity.Companion  getMapOf -androidx.activity.ComponentActivity.Companion  getMutableListOf -androidx.activity.ComponentActivity.Companion  getSHOWConfirmDialog -androidx.activity.ComponentActivity.Companion  getSHOWMessageDialog -androidx.activity.ComponentActivity.Companion  getSHOWSettingDialog -androidx.activity.ComponentActivity.Companion  getShowConfirmDialog -androidx.activity.ComponentActivity.Companion  getShowMessageDialog -androidx.activity.ComponentActivity.Companion  getShowSettingDialog -androidx.activity.ComponentActivity.Companion  	getString -androidx.activity.ComponentActivity.Companion  getTO -androidx.activity.ComponentActivity.Companion  getTOInt -androidx.activity.ComponentActivity.Companion  getTOString -androidx.activity.ComponentActivity.Companion  getTOTypedArray -androidx.activity.ComponentActivity.Companion  getTRIM -androidx.activity.ComponentActivity.Companion  getTo -androidx.activity.ComponentActivity.Companion  getToInt -androidx.activity.ComponentActivity.Companion  getToString -androidx.activity.ComponentActivity.Companion  getToTypedArray -androidx.activity.ComponentActivity.Companion  getTrim -androidx.activity.ComponentActivity.Companion  getUserData -androidx.activity.ComponentActivity.Companion  handler -androidx.activity.ComponentActivity.Companion  isEmpty -androidx.activity.ComponentActivity.Companion  
isNotEmpty -androidx.activity.ComponentActivity.Companion  
isNullOrEmpty -androidx.activity.ComponentActivity.Companion  java -androidx.activity.ComponentActivity.Companion  	lastVisit -androidx.activity.ComponentActivity.Companion  let -androidx.activity.ComponentActivity.Companion  listOf -androidx.activity.ComponentActivity.Companion  mapOf -androidx.activity.ComponentActivity.Companion  
mutableListOf -androidx.activity.ComponentActivity.Companion  newTicketAdapter -androidx.activity.ComponentActivity.Companion  onBackPressedDispatcher -androidx.activity.ComponentActivity.Companion  
sharedPref -androidx.activity.ComponentActivity.Companion  showConfirmDialog -androidx.activity.ComponentActivity.Companion  showMessageDialog -androidx.activity.ComponentActivity.Companion  showSettingDialog -androidx.activity.ComponentActivity.Companion  
startActivity -androidx.activity.ComponentActivity.Companion  startMonitoringService -androidx.activity.ComponentActivity.Companion  to -androidx.activity.ComponentActivity.Companion  toInt -androidx.activity.ComponentActivity.Companion  toString -androidx.activity.ComponentActivity.Companion  toTypedArray -androidx.activity.ComponentActivity.Companion  trim -androidx.activity.ComponentActivity.Companion  username -androidx.activity.ComponentActivity.Companion  Date 'androidx.activity.OnBackPressedCallback  Intent 'androidx.activity.OnBackPressedCallback  Locale 'androidx.activity.OnBackPressedCallback  
LoginActivity 'androidx.activity.OnBackPressedCallback  R 'androidx.activity.OnBackPressedCallback  SimpleDateFormat 'androidx.activity.OnBackPressedCallback  UserInfoManager 'androidx.activity.OnBackPressedCallback  applicationContext 'androidx.activity.OnBackPressedCallback  apply 'androidx.activity.OnBackPressedCallback  exitProcess 'androidx.activity.OnBackPressedCallback  finish 'androidx.activity.OnBackPressedCallback  finishAffinity 'androidx.activity.OnBackPressedCallback  	getString 'androidx.activity.OnBackPressedCallback  java 'androidx.activity.OnBackPressedCallback  
sharedPref 'androidx.activity.OnBackPressedCallback  showConfirmDialog 'androidx.activity.OnBackPressedCallback  
startActivity 'androidx.activity.OnBackPressedCallback  username 'androidx.activity.OnBackPressedCallback  addCallback )androidx.activity.OnBackPressedDispatcher  
onBackPressed )androidx.activity.OnBackPressedDispatcher  AppCompatActivity androidx.appcompat.app  AppCompatDelegate androidx.appcompat.app  AcceptedTicketAdapter (androidx.appcompat.app.AppCompatActivity  AcceptedTicketDto (androidx.appcompat.app.AppCompatActivity  AcceptedTicketListActivity (androidx.appcompat.app.AppCompatActivity  ActivityCompat (androidx.appcompat.app.AppCompatActivity  AdapterView (androidx.appcompat.app.AppCompatActivity  	ApiClient (androidx.appcompat.app.AppCompatActivity  ApiInterface (androidx.appcompat.app.AppCompatActivity  AppCompatDelegate (androidx.appcompat.app.AppCompatActivity  ArrayAdapter (androidx.appcompat.app.AppCompatActivity  Boolean (androidx.appcompat.app.AppCompatActivity  Build (androidx.appcompat.app.AppCompatActivity  Bundle (androidx.appcompat.app.AppCompatActivity  Button (androidx.appcompat.app.AppCompatActivity  Call (androidx.appcompat.app.AppCompatActivity  Callback (androidx.appcompat.app.AppCompatActivity  
Configuration (androidx.appcompat.app.AppCompatActivity  Context (androidx.appcompat.app.AppCompatActivity  
ContextCompat (androidx.appcompat.app.AppCompatActivity  CreateDialog (androidx.appcompat.app.AppCompatActivity  Date (androidx.appcompat.app.AppCompatActivity  EditText (androidx.appcompat.app.AppCompatActivity  FcmTokenRequest (androidx.appcompat.app.AppCompatActivity  FirebaseMessaging (androidx.appcompat.app.AppCompatActivity  GridLayoutManager (androidx.appcompat.app.AppCompatActivity  Handler (androidx.appcompat.app.AppCompatActivity  
HandlerThread (androidx.appcompat.app.AppCompatActivity  Int (androidx.appcompat.app.AppCompatActivity  Intent (androidx.appcompat.app.AppCompatActivity  List (androidx.appcompat.app.AppCompatActivity  Locale (androidx.appcompat.app.AppCompatActivity  LocaleListCompat (androidx.appcompat.app.AppCompatActivity  Log (androidx.appcompat.app.AppCompatActivity  
LoginActivity (androidx.appcompat.app.AppCompatActivity  	LoginInfo (androidx.appcompat.app.AppCompatActivity  
LoginResponse (androidx.appcompat.app.AppCompatActivity  Long (androidx.appcompat.app.AppCompatActivity  Manifest (androidx.appcompat.app.AppCompatActivity  Map (androidx.appcompat.app.AppCompatActivity  MutableList (androidx.appcompat.app.AppCompatActivity  NewTicketAdapter (androidx.appcompat.app.AppCompatActivity  
NewTicketInfo (androidx.appcompat.app.AppCompatActivity  NewTicketListActivity (androidx.appcompat.app.AppCompatActivity  NewTicketListResponse (androidx.appcompat.app.AppCompatActivity  OnBackPressedCallback (androidx.appcompat.app.AppCompatActivity  PackageManager (androidx.appcompat.app.AppCompatActivity  PowerManager (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  RecyclerView (androidx.appcompat.app.AppCompatActivity  RegisterActivity (androidx.appcompat.app.AppCompatActivity  RegisterInfo (androidx.appcompat.app.AppCompatActivity  RegisterResponse (androidx.appcompat.app.AppCompatActivity  Response (androidx.appcompat.app.AppCompatActivity  Settings (androidx.appcompat.app.AppCompatActivity  
SharedPref (androidx.appcompat.app.AppCompatActivity  SimpleDateFormat (androidx.appcompat.app.AppCompatActivity  Spinner (androidx.appcompat.app.AppCompatActivity  StandardTemplate (androidx.appcompat.app.AppCompatActivity  String (androidx.appcompat.app.AppCompatActivity  SuppressLint (androidx.appcompat.app.AppCompatActivity  TextView (androidx.appcompat.app.AppCompatActivity  	Throwable (androidx.appcompat.app.AppCompatActivity  
TicketDone (androidx.appcompat.app.AppCompatActivity  TicketMonitoring (androidx.appcompat.app.AppCompatActivity  Toast (androidx.appcompat.app.AppCompatActivity  Unit (androidx.appcompat.app.AppCompatActivity  Uri (androidx.appcompat.app.AppCompatActivity  UserDetails (androidx.appcompat.app.AppCompatActivity  UserInfoManager (androidx.appcompat.app.AppCompatActivity  View (androidx.appcompat.app.AppCompatActivity  
ViewCompat (androidx.appcompat.app.AppCompatActivity  Void (androidx.appcompat.app.AppCompatActivity  WindowInsetsCompat (androidx.appcompat.app.AppCompatActivity  acceptTicket (androidx.appcompat.app.AppCompatActivity  acceptedTicketAdapter (androidx.appcompat.app.AppCompatActivity  acceptedTicketList (androidx.appcompat.app.AppCompatActivity  applicationContext (androidx.appcompat.app.AppCompatActivity  apply (androidx.appcompat.app.AppCompatActivity  attachBaseContext (androidx.appcompat.app.AppCompatActivity  call_new_ticket_list (androidx.appcompat.app.AppCompatActivity  changeLanguage (androidx.appcompat.app.AppCompatActivity  checkPermission (androidx.appcompat.app.AppCompatActivity  dLocale (androidx.appcompat.app.AppCompatActivity  enableEdgeToEdge (androidx.appcompat.app.AppCompatActivity  exitProcess (androidx.appcompat.app.AppCompatActivity  fetchAcceptedTickets (androidx.appcompat.app.AppCompatActivity  fetchNewTicketList (androidx.appcompat.app.AppCompatActivity  findViewById (androidx.appcompat.app.AppCompatActivity  finish (androidx.appcompat.app.AppCompatActivity  finishAffinity (androidx.appcompat.app.AppCompatActivity  getAcceptedTicketList (androidx.appcompat.app.AppCompatActivity  	getString (androidx.appcompat.app.AppCompatActivity  getSystemService (androidx.appcompat.app.AppCompatActivity  getUserData (androidx.appcompat.app.AppCompatActivity  handler (androidx.appcompat.app.AppCompatActivity  isEmpty (androidx.appcompat.app.AppCompatActivity  
isNotEmpty (androidx.appcompat.app.AppCompatActivity  
isNullOrEmpty (androidx.appcompat.app.AppCompatActivity  java (androidx.appcompat.app.AppCompatActivity  	lastVisit (androidx.appcompat.app.AppCompatActivity  let (androidx.appcompat.app.AppCompatActivity  listOf (androidx.appcompat.app.AppCompatActivity  login (androidx.appcompat.app.AppCompatActivity  mapOf (androidx.appcompat.app.AppCompatActivity  maybeRequestBatteryWhitelist (androidx.appcompat.app.AppCompatActivity  
mutableListOf (androidx.appcompat.app.AppCompatActivity  newTicketAdapter (androidx.appcompat.app.AppCompatActivity  onBackPressedDispatcher (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  	onDestroy (androidx.appcompat.app.AppCompatActivity  onPause (androidx.appcompat.app.AppCompatActivity  onResume (androidx.appcompat.app.AppCompatActivity  recreate (androidx.appcompat.app.AppCompatActivity  register (androidx.appcompat.app.AppCompatActivity  registerFCMToken (androidx.appcompat.app.AppCompatActivity  	retrofit2 (androidx.appcompat.app.AppCompatActivity  sendFcmToken (androidx.appcompat.app.AppCompatActivity  setContentView (androidx.appcompat.app.AppCompatActivity  
sharedPref (androidx.appcompat.app.AppCompatActivity  showConfirmDialog (androidx.appcompat.app.AppCompatActivity  showMessageDialog (androidx.appcompat.app.AppCompatActivity  showSettingDialog (androidx.appcompat.app.AppCompatActivity  solvedTicket (androidx.appcompat.app.AppCompatActivity  
startActivity (androidx.appcompat.app.AppCompatActivity  startMonitoringService (androidx.appcompat.app.AppCompatActivity  to (androidx.appcompat.app.AppCompatActivity  toInt (androidx.appcompat.app.AppCompatActivity  toString (androidx.appcompat.app.AppCompatActivity  toTypedArray (androidx.appcompat.app.AppCompatActivity  trim (androidx.appcompat.app.AppCompatActivity  username (androidx.appcompat.app.AppCompatActivity  setApplicationLocales (androidx.appcompat.app.AppCompatDelegate  CardView androidx.cardview.widget  setOnClickListener !androidx.cardview.widget.CardView  ActivityCompat androidx.core.app  NotificationCompat androidx.core.app  NotificationManagerCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  AcceptedTicketAdapter #androidx.core.app.ComponentActivity  AcceptedTicketDto #androidx.core.app.ComponentActivity  AcceptedTicketListActivity #androidx.core.app.ComponentActivity  ActivityCompat #androidx.core.app.ComponentActivity  AdapterView #androidx.core.app.ComponentActivity  	ApiClient #androidx.core.app.ComponentActivity  ApiInterface #androidx.core.app.ComponentActivity  AppCompatDelegate #androidx.core.app.ComponentActivity  ArrayAdapter #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Build #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  Call #androidx.core.app.ComponentActivity  Callback #androidx.core.app.ComponentActivity  
Configuration #androidx.core.app.ComponentActivity  Context #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  CreateDialog #androidx.core.app.ComponentActivity  Date #androidx.core.app.ComponentActivity  EditText #androidx.core.app.ComponentActivity  FcmTokenRequest #androidx.core.app.ComponentActivity  FirebaseMessaging #androidx.core.app.ComponentActivity  GridLayoutManager #androidx.core.app.ComponentActivity  Handler #androidx.core.app.ComponentActivity  
HandlerThread #androidx.core.app.ComponentActivity  Int #androidx.core.app.ComponentActivity  Intent #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Locale #androidx.core.app.ComponentActivity  LocaleListCompat #androidx.core.app.ComponentActivity  Log #androidx.core.app.ComponentActivity  
LoginActivity #androidx.core.app.ComponentActivity  	LoginInfo #androidx.core.app.ComponentActivity  
LoginResponse #androidx.core.app.ComponentActivity  Long #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  Map #androidx.core.app.ComponentActivity  MutableList #androidx.core.app.ComponentActivity  NewTicketAdapter #androidx.core.app.ComponentActivity  
NewTicketInfo #androidx.core.app.ComponentActivity  NewTicketListActivity #androidx.core.app.ComponentActivity  NewTicketListResponse #androidx.core.app.ComponentActivity  OnBackPressedCallback #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  PowerManager #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  RecyclerView #androidx.core.app.ComponentActivity  RegisterActivity #androidx.core.app.ComponentActivity  RegisterInfo #androidx.core.app.ComponentActivity  RegisterResponse #androidx.core.app.ComponentActivity  Response #androidx.core.app.ComponentActivity  Settings #androidx.core.app.ComponentActivity  
SharedPref #androidx.core.app.ComponentActivity  SimpleDateFormat #androidx.core.app.ComponentActivity  Spinner #androidx.core.app.ComponentActivity  StandardTemplate #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  SuppressLint #androidx.core.app.ComponentActivity  TextView #androidx.core.app.ComponentActivity  	Throwable #androidx.core.app.ComponentActivity  
TicketDone #androidx.core.app.ComponentActivity  TicketMonitoring #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  UserDetails #androidx.core.app.ComponentActivity  UserInfoManager #androidx.core.app.ComponentActivity  View #androidx.core.app.ComponentActivity  
ViewCompat #androidx.core.app.ComponentActivity  Void #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  acceptTicket #androidx.core.app.ComponentActivity  acceptedTicketAdapter #androidx.core.app.ComponentActivity  acceptedTicketList #androidx.core.app.ComponentActivity  applicationContext #androidx.core.app.ComponentActivity  apply #androidx.core.app.ComponentActivity  attachBaseContext #androidx.core.app.ComponentActivity  call_new_ticket_list #androidx.core.app.ComponentActivity  changeLanguage #androidx.core.app.ComponentActivity  checkPermission #androidx.core.app.ComponentActivity  dLocale #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  exitProcess #androidx.core.app.ComponentActivity  fetchAcceptedTickets #androidx.core.app.ComponentActivity  fetchNewTicketList #androidx.core.app.ComponentActivity  findViewById #androidx.core.app.ComponentActivity  finish #androidx.core.app.ComponentActivity  finishAffinity #androidx.core.app.ComponentActivity  getAcceptedTicketList #androidx.core.app.ComponentActivity  	getString #androidx.core.app.ComponentActivity  getSystemService #androidx.core.app.ComponentActivity  getUserData #androidx.core.app.ComponentActivity  handler #androidx.core.app.ComponentActivity  isEmpty #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
isNullOrEmpty #androidx.core.app.ComponentActivity  java #androidx.core.app.ComponentActivity  	lastVisit #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  listOf #androidx.core.app.ComponentActivity  login #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  maybeRequestBatteryWhitelist #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  newTicketAdapter #androidx.core.app.ComponentActivity  onBackPressedDispatcher #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  	onDestroy #androidx.core.app.ComponentActivity  onPause #androidx.core.app.ComponentActivity  onResume #androidx.core.app.ComponentActivity  recreate #androidx.core.app.ComponentActivity  register #androidx.core.app.ComponentActivity  registerFCMToken #androidx.core.app.ComponentActivity  	retrofit2 #androidx.core.app.ComponentActivity  sendFcmToken #androidx.core.app.ComponentActivity  setContentView #androidx.core.app.ComponentActivity  
sharedPref #androidx.core.app.ComponentActivity  showConfirmDialog #androidx.core.app.ComponentActivity  showMessageDialog #androidx.core.app.ComponentActivity  showSettingDialog #androidx.core.app.ComponentActivity  solvedTicket #androidx.core.app.ComponentActivity  
startActivity #androidx.core.app.ComponentActivity  startMonitoringService #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  toInt #androidx.core.app.ComponentActivity  toString #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  trim #androidx.core.app.ComponentActivity  username #androidx.core.app.ComponentActivity  Builder $androidx.core.app.NotificationCompat  FOREGROUND_SERVICE_IMMEDIATE $androidx.core.app.NotificationCompat  PRIORITY_DEFAULT $androidx.core.app.NotificationCompat  
PRIORITY_HIGH $androidx.core.app.NotificationCompat  PRIORITY_LOW $androidx.core.app.NotificationCompat  build ,androidx.core.app.NotificationCompat.Builder  
setAutoCancel ,androidx.core.app.NotificationCompat.Builder  setContentIntent ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  setForegroundServiceBehavior ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setPriority ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  areNotificationsEnabled +androidx.core.app.NotificationManagerCompat  from +androidx.core.app.NotificationManagerCompat  notify +androidx.core.app.NotificationManagerCompat  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  startForegroundService #androidx.core.content.ContextCompat  Insets androidx.core.graphics  bottom androidx.core.graphics.Insets  left androidx.core.graphics.Insets  right androidx.core.graphics.Insets  top androidx.core.graphics.Insets  LocaleListCompat androidx.core.os  forLanguageTags !androidx.core.os.LocaleListCompat  
ViewCompat androidx.core.view  WindowInsetsCompat androidx.core.view  <SAM-CONSTRUCTOR> .androidx.core.view.OnApplyWindowInsetsListener  setOnApplyWindowInsetsListener androidx.core.view.ViewCompat  Type %androidx.core.view.WindowInsetsCompat  	getInsets %androidx.core.view.WindowInsetsCompat  
systemBars *androidx.core.view.WindowInsetsCompat.Type  AcceptedTicketAdapter &androidx.fragment.app.FragmentActivity  AcceptedTicketDto &androidx.fragment.app.FragmentActivity  AcceptedTicketListActivity &androidx.fragment.app.FragmentActivity  ActivityCompat &androidx.fragment.app.FragmentActivity  AdapterView &androidx.fragment.app.FragmentActivity  	ApiClient &androidx.fragment.app.FragmentActivity  ApiInterface &androidx.fragment.app.FragmentActivity  AppCompatDelegate &androidx.fragment.app.FragmentActivity  ArrayAdapter &androidx.fragment.app.FragmentActivity  Boolean &androidx.fragment.app.FragmentActivity  Build &androidx.fragment.app.FragmentActivity  Bundle &androidx.fragment.app.FragmentActivity  Button &androidx.fragment.app.FragmentActivity  Call &androidx.fragment.app.FragmentActivity  Callback &androidx.fragment.app.FragmentActivity  
Configuration &androidx.fragment.app.FragmentActivity  Context &androidx.fragment.app.FragmentActivity  
ContextCompat &androidx.fragment.app.FragmentActivity  CreateDialog &androidx.fragment.app.FragmentActivity  Date &androidx.fragment.app.FragmentActivity  EditText &androidx.fragment.app.FragmentActivity  FcmTokenRequest &androidx.fragment.app.FragmentActivity  FirebaseMessaging &androidx.fragment.app.FragmentActivity  GridLayoutManager &androidx.fragment.app.FragmentActivity  Handler &androidx.fragment.app.FragmentActivity  
HandlerThread &androidx.fragment.app.FragmentActivity  Int &androidx.fragment.app.FragmentActivity  Intent &androidx.fragment.app.FragmentActivity  List &androidx.fragment.app.FragmentActivity  Locale &androidx.fragment.app.FragmentActivity  LocaleListCompat &androidx.fragment.app.FragmentActivity  Log &androidx.fragment.app.FragmentActivity  
LoginActivity &androidx.fragment.app.FragmentActivity  	LoginInfo &androidx.fragment.app.FragmentActivity  
LoginResponse &androidx.fragment.app.FragmentActivity  Long &androidx.fragment.app.FragmentActivity  Manifest &androidx.fragment.app.FragmentActivity  Map &androidx.fragment.app.FragmentActivity  MutableList &androidx.fragment.app.FragmentActivity  NewTicketAdapter &androidx.fragment.app.FragmentActivity  
NewTicketInfo &androidx.fragment.app.FragmentActivity  NewTicketListActivity &androidx.fragment.app.FragmentActivity  NewTicketListResponse &androidx.fragment.app.FragmentActivity  OnBackPressedCallback &androidx.fragment.app.FragmentActivity  PackageManager &androidx.fragment.app.FragmentActivity  PowerManager &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  RecyclerView &androidx.fragment.app.FragmentActivity  RegisterActivity &androidx.fragment.app.FragmentActivity  RegisterInfo &androidx.fragment.app.FragmentActivity  RegisterResponse &androidx.fragment.app.FragmentActivity  Response &androidx.fragment.app.FragmentActivity  Settings &androidx.fragment.app.FragmentActivity  
SharedPref &androidx.fragment.app.FragmentActivity  SimpleDateFormat &androidx.fragment.app.FragmentActivity  Spinner &androidx.fragment.app.FragmentActivity  StandardTemplate &androidx.fragment.app.FragmentActivity  String &androidx.fragment.app.FragmentActivity  SuppressLint &androidx.fragment.app.FragmentActivity  TextView &androidx.fragment.app.FragmentActivity  	Throwable &androidx.fragment.app.FragmentActivity  
TicketDone &androidx.fragment.app.FragmentActivity  TicketMonitoring &androidx.fragment.app.FragmentActivity  Toast &androidx.fragment.app.FragmentActivity  Unit &androidx.fragment.app.FragmentActivity  Uri &androidx.fragment.app.FragmentActivity  UserDetails &androidx.fragment.app.FragmentActivity  UserInfoManager &androidx.fragment.app.FragmentActivity  View &androidx.fragment.app.FragmentActivity  
ViewCompat &androidx.fragment.app.FragmentActivity  Void &androidx.fragment.app.FragmentActivity  WindowInsetsCompat &androidx.fragment.app.FragmentActivity  acceptTicket &androidx.fragment.app.FragmentActivity  acceptedTicketAdapter &androidx.fragment.app.FragmentActivity  acceptedTicketList &androidx.fragment.app.FragmentActivity  applicationContext &androidx.fragment.app.FragmentActivity  apply &androidx.fragment.app.FragmentActivity  attachBaseContext &androidx.fragment.app.FragmentActivity  call_new_ticket_list &androidx.fragment.app.FragmentActivity  changeLanguage &androidx.fragment.app.FragmentActivity  checkPermission &androidx.fragment.app.FragmentActivity  dLocale &androidx.fragment.app.FragmentActivity  enableEdgeToEdge &androidx.fragment.app.FragmentActivity  exitProcess &androidx.fragment.app.FragmentActivity  fetchAcceptedTickets &androidx.fragment.app.FragmentActivity  fetchNewTicketList &androidx.fragment.app.FragmentActivity  findViewById &androidx.fragment.app.FragmentActivity  finish &androidx.fragment.app.FragmentActivity  finishAffinity &androidx.fragment.app.FragmentActivity  getAcceptedTicketList &androidx.fragment.app.FragmentActivity  	getString &androidx.fragment.app.FragmentActivity  getSystemService &androidx.fragment.app.FragmentActivity  getUserData &androidx.fragment.app.FragmentActivity  handler &androidx.fragment.app.FragmentActivity  isEmpty &androidx.fragment.app.FragmentActivity  
isNotEmpty &androidx.fragment.app.FragmentActivity  
isNullOrEmpty &androidx.fragment.app.FragmentActivity  java &androidx.fragment.app.FragmentActivity  	lastVisit &androidx.fragment.app.FragmentActivity  let &androidx.fragment.app.FragmentActivity  listOf &androidx.fragment.app.FragmentActivity  login &androidx.fragment.app.FragmentActivity  mapOf &androidx.fragment.app.FragmentActivity  maybeRequestBatteryWhitelist &androidx.fragment.app.FragmentActivity  
mutableListOf &androidx.fragment.app.FragmentActivity  newTicketAdapter &androidx.fragment.app.FragmentActivity  onBackPressedDispatcher &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  	onDestroy &androidx.fragment.app.FragmentActivity  onPause &androidx.fragment.app.FragmentActivity  onResume &androidx.fragment.app.FragmentActivity  recreate &androidx.fragment.app.FragmentActivity  register &androidx.fragment.app.FragmentActivity  registerFCMToken &androidx.fragment.app.FragmentActivity  	retrofit2 &androidx.fragment.app.FragmentActivity  sendFcmToken &androidx.fragment.app.FragmentActivity  setContentView &androidx.fragment.app.FragmentActivity  
sharedPref &androidx.fragment.app.FragmentActivity  showConfirmDialog &androidx.fragment.app.FragmentActivity  showMessageDialog &androidx.fragment.app.FragmentActivity  showSettingDialog &androidx.fragment.app.FragmentActivity  solvedTicket &androidx.fragment.app.FragmentActivity  
startActivity &androidx.fragment.app.FragmentActivity  startMonitoringService &androidx.fragment.app.FragmentActivity  to &androidx.fragment.app.FragmentActivity  toInt &androidx.fragment.app.FragmentActivity  toString &androidx.fragment.app.FragmentActivity  toTypedArray &androidx.fragment.app.FragmentActivity  trim &androidx.fragment.app.FragmentActivity  username &androidx.fragment.app.FragmentActivity  DiffUtil androidx.recyclerview.widget  GridLayoutManager androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
LayoutManager )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  adapter )androidx.recyclerview.widget.RecyclerView  
getADAPTER )androidx.recyclerview.widget.RecyclerView  
getAdapter )androidx.recyclerview.widget.RecyclerView  getLAYOUTManager )androidx.recyclerview.widget.RecyclerView  getLayoutManager )androidx.recyclerview.widget.RecyclerView  
layoutManager )androidx.recyclerview.widget.RecyclerView  
setAdapter )androidx.recyclerview.widget.RecyclerView  setLayoutManager )androidx.recyclerview.widget.RecyclerView  AcceptedTicketDetailsActivity 1androidx.recyclerview.widget.RecyclerView.Adapter  AcceptedTicketDto 1androidx.recyclerview.widget.RecyclerView.Adapter  CardView 1androidx.recyclerview.widget.RecyclerView.Adapter  Context 1androidx.recyclerview.widget.RecyclerView.Adapter  Int 1androidx.recyclerview.widget.RecyclerView.Adapter  Intent 1androidx.recyclerview.widget.RecyclerView.Adapter  LayoutInflater 1androidx.recyclerview.widget.RecyclerView.Adapter  List 1androidx.recyclerview.widget.RecyclerView.Adapter  MutableList 1androidx.recyclerview.widget.RecyclerView.Adapter  NewTicketDetailsActivity 1androidx.recyclerview.widget.RecyclerView.Adapter  NewTicketListResponse 1androidx.recyclerview.widget.RecyclerView.Adapter  R 1androidx.recyclerview.widget.RecyclerView.Adapter  RecyclerView 1androidx.recyclerview.widget.RecyclerView.Adapter  
SharedPref 1androidx.recyclerview.widget.RecyclerView.Adapter  String 1androidx.recyclerview.widget.RecyclerView.Adapter  TextView 1androidx.recyclerview.widget.RecyclerView.Adapter  TicketViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  apply 1androidx.recyclerview.widget.RecyclerView.Adapter  java 1androidx.recyclerview.widget.RecyclerView.Adapter  map 1androidx.recyclerview.widget.RecyclerView.Adapter  notifyDataSetChanged 1androidx.recyclerview.widget.RecyclerView.Adapter  toSet 1androidx.recyclerview.widget.RecyclerView.Adapter  
updateData 1androidx.recyclerview.widget.RecyclerView.Adapter  CardView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R 4androidx.recyclerview.widget.RecyclerView.ViewHolder  TextView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  View 4androidx.recyclerview.widget.RecyclerView.ViewHolder  R com.example.standardtemplate  AcceptedTicketAdapter 6com.example.standardtemplate.Activities.AcceptedTicket  AcceptedTicketDetailsActivity 6com.example.standardtemplate.Activities.AcceptedTicket  AcceptedTicketListActivity 6com.example.standardtemplate.Activities.AcceptedTicket  	ApiClient 6com.example.standardtemplate.Activities.AcceptedTicket  ApiInterface 6com.example.standardtemplate.Activities.AcceptedTicket  GridLayoutManager 6com.example.standardtemplate.Activities.AcceptedTicket  Int 6com.example.standardtemplate.Activities.AcceptedTicket  List 6com.example.standardtemplate.Activities.AcceptedTicket  Log 6com.example.standardtemplate.Activities.AcceptedTicket  MutableList 6com.example.standardtemplate.Activities.AcceptedTicket  R 6com.example.standardtemplate.Activities.AcceptedTicket  
SharedPref 6com.example.standardtemplate.Activities.AcceptedTicket  String 6com.example.standardtemplate.Activities.AcceptedTicket  	Throwable 6com.example.standardtemplate.Activities.AcceptedTicket  
TicketDone 6com.example.standardtemplate.Activities.AcceptedTicket  Toast 6com.example.standardtemplate.Activities.AcceptedTicket  UserInfoManager 6com.example.standardtemplate.Activities.AcceptedTicket  
ViewCompat 6com.example.standardtemplate.Activities.AcceptedTicket  Void 6com.example.standardtemplate.Activities.AcceptedTicket  WindowInsetsCompat 6com.example.standardtemplate.Activities.AcceptedTicket  acceptedTicketAdapter 6com.example.standardtemplate.Activities.AcceptedTicket  acceptedTicketList 6com.example.standardtemplate.Activities.AcceptedTicket  applicationContext 6com.example.standardtemplate.Activities.AcceptedTicket  enableEdgeToEdge 6com.example.standardtemplate.Activities.AcceptedTicket  finish 6com.example.standardtemplate.Activities.AcceptedTicket  	getString 6com.example.standardtemplate.Activities.AcceptedTicket  isEmpty 6com.example.standardtemplate.Activities.AcceptedTicket  java 6com.example.standardtemplate.Activities.AcceptedTicket  let 6com.example.standardtemplate.Activities.AcceptedTicket  
mutableListOf 6com.example.standardtemplate.Activities.AcceptedTicket  onBackPressedDispatcher 6com.example.standardtemplate.Activities.AcceptedTicket  	retrofit2 6com.example.standardtemplate.Activities.AcceptedTicket  toString 6com.example.standardtemplate.Activities.AcceptedTicket  	ApiClient Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  ApiInterface Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Bundle Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Button Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Call Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  EditText Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Int Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Log Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  R Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Response Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  
SharedPref Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  String Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  TextView Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	Throwable Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  
TicketDone Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Toast Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  UserInfoManager Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  
ViewCompat Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  Void Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  WindowInsetsCompat Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  applicationContext Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  btnDone Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	btnReturn Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  enableEdgeToEdge Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  findViewById Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  finish Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getAPPLICATIONContext Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getApplicationContext Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getENABLEEdgeToEdge Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getEnableEdgeToEdge Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	getINTENT Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  
getISEmpty Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	getIntent Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  
getIsEmpty Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	getString Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getTOString Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getToString Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  intent Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  isEmpty Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  java Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  onBackPressedDispatcher Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	retrofit2 Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  setApplicationContext Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  setContentView Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  	setIntent Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  solvedTicket Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  toString Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  token Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  username Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity  getAPPLICATIONContext tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  getApplicationContext tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  	getFINISH tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  	getFinish tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  getGETString tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  getGetString tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  getONBackPressedDispatcher tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  getOnBackPressedDispatcher tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivity.solvedTicket.<no name provided>  AcceptedTicketAdapter Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  AcceptedTicketDto Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  	ApiClient Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  ApiInterface Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Bundle Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Button Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Call Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Callback Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  GridLayoutManager Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  List Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Log Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  MutableList Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  R Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  RecyclerView Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Response Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  
SharedPref Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  	Throwable Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  Toast Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  UserInfoManager Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  
ViewCompat Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  WindowInsetsCompat Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  acceptedTicketAdapter Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  acceptedTicketList Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  
apiService Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  applicationContext Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  	btnReturn Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  enableEdgeToEdge Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  fetchAcceptedTickets Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  findViewById Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  finish Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getAPPLICATIONContext Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getAcceptedTicketList Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getApplicationContext Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getENABLEEdgeToEdge Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getEnableEdgeToEdge Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getLET Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getLet Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getMUTABLEListOf Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getMutableListOf Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  	getString Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getTOString Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getToString Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  java Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  let Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  
mutableListOf Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  onBackPressedDispatcher Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  rvAcceptedTicketList Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  setApplicationContext Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  setContentView Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  
sharedPref Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  toString Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  token Qcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity  getACCEPTEDTicketAdapter ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getACCEPTEDTicketList ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getAPPLICATIONContext ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getAcceptedTicketAdapter ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getAcceptedTicketList ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getApplicationContext ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getGETString ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getGetString ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getLET ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  getLet ycom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivity.fetchAcceptedTickets.<no name provided>  AcceptedTicketAdapter >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  AcceptedTicketDetailsActivity >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  AcceptedTicketDto >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  Int >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  Intent >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  LayoutInflater >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  List >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  R >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  String >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  TicketViewHolder >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  UserNavigation >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  apply >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  java >com.example.standardtemplate.Activities.AcceptedTicket.Adapter  AcceptedTicketDetailsActivity Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  AcceptedTicketDto Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  CardView Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  Context Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  Int Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  Intent Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  LayoutInflater Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  List Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  R Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  RecyclerView Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  
SharedPref Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  String Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  TextView Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  TicketViewHolder Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  View Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  	ViewGroup Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  acceptedTicketList Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  apply Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  getAPPLY Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  getApply Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  java Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  notifyDataSetChanged Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  username Tcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter  CardView ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  R ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  TextView ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  View ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  cardView ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  itemView ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  txtAttendBy ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  txtErrorMsg ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  txtMachineId ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  	txtStatus ecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolder  Int Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  SerializedName Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  String Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  UserNavigation Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  ackAt Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  attendBy Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  
completeAt Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  	createdAt Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  errorMsg Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  id Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  machineDowntimeStr Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  	machineId Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  
machineStatus Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  remedy Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  status Pcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketDto  Int Mcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.UserNavigation  SerializedName Mcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.UserNavigation  String Mcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.UserNavigation  ActivityCompat 5com.example.standardtemplate.Activities.Login_Setting  	ApiClient 5com.example.standardtemplate.Activities.Login_Setting  ApiInterface 5com.example.standardtemplate.Activities.Login_Setting  AppCompatDelegate 5com.example.standardtemplate.Activities.Login_Setting  ArrayAdapter 5com.example.standardtemplate.Activities.Login_Setting  Build 5com.example.standardtemplate.Activities.Login_Setting  Context 5com.example.standardtemplate.Activities.Login_Setting  
ContextCompat 5com.example.standardtemplate.Activities.Login_Setting  FcmTokenRequest 5com.example.standardtemplate.Activities.Login_Setting  FirebaseMessaging 5com.example.standardtemplate.Activities.Login_Setting  Int 5com.example.standardtemplate.Activities.Login_Setting  Intent 5com.example.standardtemplate.Activities.Login_Setting  List 5com.example.standardtemplate.Activities.Login_Setting  LocaleListCompat 5com.example.standardtemplate.Activities.Login_Setting  Log 5com.example.standardtemplate.Activities.Login_Setting  
LoginActivity 5com.example.standardtemplate.Activities.Login_Setting  	LoginInfo 5com.example.standardtemplate.Activities.Login_Setting  Long 5com.example.standardtemplate.Activities.Login_Setting  Manifest 5com.example.standardtemplate.Activities.Login_Setting  Map 5com.example.standardtemplate.Activities.Login_Setting  NewTicketListActivity 5com.example.standardtemplate.Activities.Login_Setting  PackageManager 5com.example.standardtemplate.Activities.Login_Setting  R 5com.example.standardtemplate.Activities.Login_Setting  RegisterActivity 5com.example.standardtemplate.Activities.Login_Setting  Settings 5com.example.standardtemplate.Activities.Login_Setting  
SharedPref 5com.example.standardtemplate.Activities.Login_Setting  String 5com.example.standardtemplate.Activities.Login_Setting  	Throwable 5com.example.standardtemplate.Activities.Login_Setting  Toast 5com.example.standardtemplate.Activities.Login_Setting  Uri 5com.example.standardtemplate.Activities.Login_Setting  UserInfoManager 5com.example.standardtemplate.Activities.Login_Setting  
ViewCompat 5com.example.standardtemplate.Activities.Login_Setting  Void 5com.example.standardtemplate.Activities.Login_Setting  WindowInsetsCompat 5com.example.standardtemplate.Activities.Login_Setting  applicationContext 5com.example.standardtemplate.Activities.Login_Setting  apply 5com.example.standardtemplate.Activities.Login_Setting  changeLanguage 5com.example.standardtemplate.Activities.Login_Setting  enableEdgeToEdge 5com.example.standardtemplate.Activities.Login_Setting  exitProcess 5com.example.standardtemplate.Activities.Login_Setting  finish 5com.example.standardtemplate.Activities.Login_Setting  finishAffinity 5com.example.standardtemplate.Activities.Login_Setting  	getString 5com.example.standardtemplate.Activities.Login_Setting  getUserData 5com.example.standardtemplate.Activities.Login_Setting  isEmpty 5com.example.standardtemplate.Activities.Login_Setting  
isNotEmpty 5com.example.standardtemplate.Activities.Login_Setting  
isNullOrEmpty 5com.example.standardtemplate.Activities.Login_Setting  java 5com.example.standardtemplate.Activities.Login_Setting  	lastVisit 5com.example.standardtemplate.Activities.Login_Setting  listOf 5com.example.standardtemplate.Activities.Login_Setting  mapOf 5com.example.standardtemplate.Activities.Login_Setting  
mutableListOf 5com.example.standardtemplate.Activities.Login_Setting  showConfirmDialog 5com.example.standardtemplate.Activities.Login_Setting  showMessageDialog 5com.example.standardtemplate.Activities.Login_Setting  showSettingDialog 5com.example.standardtemplate.Activities.Login_Setting  
startActivity 5com.example.standardtemplate.Activities.Login_Setting  startMonitoringService 5com.example.standardtemplate.Activities.Login_Setting  to 5com.example.standardtemplate.Activities.Login_Setting  toString 5com.example.standardtemplate.Activities.Login_Setting  toTypedArray 5com.example.standardtemplate.Activities.Login_Setting  	ApiClient <com.example.standardtemplate.Activities.Login_Setting.Dialog  Dialog <com.example.standardtemplate.Activities.Login_Setting.Dialog  Intent <com.example.standardtemplate.Activities.Login_Setting.Dialog  LayoutInflater <com.example.standardtemplate.Activities.Login_Setting.Dialog  
LoginActivity <com.example.standardtemplate.Activities.Login_Setting.Dialog  R <com.example.standardtemplate.Activities.Login_Setting.Dialog  
SettingDialog <com.example.standardtemplate.Activities.Login_Setting.Dialog  
SharedPref <com.example.standardtemplate.Activities.Login_Setting.Dialog  String <com.example.standardtemplate.Activities.Login_Setting.Dialog  Window <com.example.standardtemplate.Activities.Login_Setting.Dialog  android <com.example.standardtemplate.Activities.Login_Setting.Dialog  endsWith <com.example.standardtemplate.Activities.Login_Setting.Dialog  isEmpty <com.example.standardtemplate.Activities.Login_Setting.Dialog  java <com.example.standardtemplate.Activities.Login_Setting.Dialog  showMessageDialog <com.example.standardtemplate.Activities.Login_Setting.Dialog  
startsWith <com.example.standardtemplate.Activities.Login_Setting.Dialog  Activity Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  	ApiClient Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  Button Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  Context Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  Dialog Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  EditText Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  	ImageView Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  Intent Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  LayoutInflater Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
LoginActivity Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  R Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
SharedPref Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  String Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  Window Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  android Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  endsWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getANDROID Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getAndroid Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  getENDSWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  getEndsWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getISEmpty Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getIsEmpty Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  getSHOWMessageDialog Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getSTARTSWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  getShowMessageDialog Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
getStartsWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  isEmpty Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  java Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
sharedPref Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  showMessageDialog Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  showSettingDialog Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  
startsWith Jcom.example.standardtemplate.Activities.Login_Setting.Dialog.SettingDialog  ActivityCompat Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  AdapterView Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	ApiClient Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  ApiInterface Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  AppCompatDelegate Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  ArrayAdapter Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Build Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Bundle Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Button Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Call Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Callback Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Context Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
ContextCompat Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  EditText Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  FcmTokenRequest Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  FirebaseMessaging Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Int Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Intent Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  List Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  LocaleListCompat Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Log Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
LoginActivity Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	LoginInfo Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
LoginResponse Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Long Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Manifest Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Map Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  NewTicketListActivity Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  OnBackPressedCallback Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  PackageManager Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  PowerManager Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  R Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  REQUEST_PERMISSIONS Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  RegisterActivity Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Response Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Settings Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
SharedPref Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Spinner Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  StandardTemplate Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  String Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  SuppressLint Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  TextView Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	Throwable Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Toast Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Uri Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  UserDetails Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  UserInfoManager Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  View Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
ViewCompat Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  Void Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  WindowInsetsCompat Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
apiService Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  application Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  applicationContext Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  apply Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  btnLogin Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
btnSetting Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  changeLanguage Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  checkPermission Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  deviceToken Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  enableEdgeToEdge Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  exitProcess Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  findViewById Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  finish Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  finishAffinity Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getAPPLICATION Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getAPPLICATIONContext Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getAPPLY Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getApplication Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getApplicationContext Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getApply Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getENABLEEdgeToEdge Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getEXITProcess Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getEnableEdgeToEdge Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getExitProcess Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
getISEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
getISNotEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getISNullOrEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
getIsEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
getIsNotEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getIsNullOrEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	getLISTOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	getListOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getMAPOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getMUTABLEListOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getMapOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getMutableListOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getPACKAGEName Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getPackageName Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getSHOWConfirmDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getSHOWMessageDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getSHOWSettingDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getShowConfirmDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getShowMessageDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getShowSettingDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	getString Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getSystemService Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getTO Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getTOString Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getTOTypedArray Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getTo Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getToString Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getToTypedArray Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getUserData Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
hlRegister Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  isEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
isNotEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
isNullOrEmpty Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  java Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  	lastVisit Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  listOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  login Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  mapOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  maybeRequestBatteryWhitelist Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
mutableListOf Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  onBackPressedDispatcher Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  packageName Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  registerFCMToken Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  sendFcmToken Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  setApplication Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  setApplicationContext Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  setContentView Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  setPackageName Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
sharedPref Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  showConfirmDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  showMessageDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  showSettingDialog Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  spinner Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  
startActivity Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  startMonitoringService Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  to Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  toString Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  toTypedArray Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  txtPass Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  txtUsername Ccom.example.standardtemplate.Activities.Login_Setting.LoginActivity  getAPPLICATIONContext bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getApplicationContext bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getGETString bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getGetString bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getSHOWMessageDialog bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getShowMessageDialog bcom.example.standardtemplate.Activities.Login_Setting.LoginActivity.getUserData.<no name provided>  getAPPLICATIONContext \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getApplicationContext \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  	getFINISH \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  	getFinish \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getGETString \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getGETUserData \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getGetString \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getGetUserData \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getLASTVisit \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getLastVisit \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getSHOWMessageDialog \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getSTARTActivity \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getSTARTMonitoringService \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getShowMessageDialog \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getStartActivity \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getStartMonitoringService \com.example.standardtemplate.Activities.Login_Setting.LoginActivity.login.<no name provided>  getCHANGELanguage _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getChangeLanguage _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getEXITProcess _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getExitProcess _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getFINISHAffinity _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getFinishAffinity _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getGETString _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getGetString _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getSHOWConfirmDialog _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getShowConfirmDialog _com.example.standardtemplate.Activities.Login_Setting.LoginActivity.onCreate.<no name provided>  getAPPLICATIONContext ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  getApplicationContext ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  getGETString ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  getGetString ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  getSHOWMessageDialog ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  getShowMessageDialog ocom.example.standardtemplate.Activities.Login_Setting.LoginActivity.sendFcmToken.<anonymous>.<no name provided>  AcceptedTicketListActivity 2com.example.standardtemplate.Activities.NewTickets  	ApiClient 2com.example.standardtemplate.Activities.NewTickets  ApiInterface 2com.example.standardtemplate.Activities.NewTickets  Boolean 2com.example.standardtemplate.Activities.NewTickets  CreateDialog 2com.example.standardtemplate.Activities.NewTickets  Date 2com.example.standardtemplate.Activities.NewTickets  GridLayoutManager 2com.example.standardtemplate.Activities.NewTickets  Handler 2com.example.standardtemplate.Activities.NewTickets  
HandlerThread 2com.example.standardtemplate.Activities.NewTickets  Intent 2com.example.standardtemplate.Activities.NewTickets  List 2com.example.standardtemplate.Activities.NewTickets  Locale 2com.example.standardtemplate.Activities.NewTickets  Log 2com.example.standardtemplate.Activities.NewTickets  
LoginActivity 2com.example.standardtemplate.Activities.NewTickets  MutableList 2com.example.standardtemplate.Activities.NewTickets  NewTicketAdapter 2com.example.standardtemplate.Activities.NewTickets  NewTicketDetailsActivity 2com.example.standardtemplate.Activities.NewTickets  
NewTicketInfo 2com.example.standardtemplate.Activities.NewTickets  NewTicketListActivity 2com.example.standardtemplate.Activities.NewTickets  R 2com.example.standardtemplate.Activities.NewTickets  
SharedPref 2com.example.standardtemplate.Activities.NewTickets  SimpleDateFormat 2com.example.standardtemplate.Activities.NewTickets  String 2com.example.standardtemplate.Activities.NewTickets  	Throwable 2com.example.standardtemplate.Activities.NewTickets  Toast 2com.example.standardtemplate.Activities.NewTickets  Unit 2com.example.standardtemplate.Activities.NewTickets  UserInfoManager 2com.example.standardtemplate.Activities.NewTickets  
ViewCompat 2com.example.standardtemplate.Activities.NewTickets  Void 2com.example.standardtemplate.Activities.NewTickets  WindowInsetsCompat 2com.example.standardtemplate.Activities.NewTickets  applicationContext 2com.example.standardtemplate.Activities.NewTickets  apply 2com.example.standardtemplate.Activities.NewTickets  call_new_ticket_list 2com.example.standardtemplate.Activities.NewTickets  enableEdgeToEdge 2com.example.standardtemplate.Activities.NewTickets  finish 2com.example.standardtemplate.Activities.NewTickets  	getString 2com.example.standardtemplate.Activities.NewTickets  handler 2com.example.standardtemplate.Activities.NewTickets  isEmpty 2com.example.standardtemplate.Activities.NewTickets  
isNotEmpty 2com.example.standardtemplate.Activities.NewTickets  java 2com.example.standardtemplate.Activities.NewTickets  let 2com.example.standardtemplate.Activities.NewTickets  
mutableListOf 2com.example.standardtemplate.Activities.NewTickets  newTicketAdapter 2com.example.standardtemplate.Activities.NewTickets  	retrofit2 2com.example.standardtemplate.Activities.NewTickets  
sharedPref 2com.example.standardtemplate.Activities.NewTickets  showConfirmDialog 2com.example.standardtemplate.Activities.NewTickets  showMessageDialog 2com.example.standardtemplate.Activities.NewTickets  
startActivity 2com.example.standardtemplate.Activities.NewTickets  toInt 2com.example.standardtemplate.Activities.NewTickets  toString 2com.example.standardtemplate.Activities.NewTickets  username 2com.example.standardtemplate.Activities.NewTickets  Int :com.example.standardtemplate.Activities.NewTickets.Adapter  Intent :com.example.standardtemplate.Activities.NewTickets.Adapter  LayoutInflater :com.example.standardtemplate.Activities.NewTickets.Adapter  List :com.example.standardtemplate.Activities.NewTickets.Adapter  MutableList :com.example.standardtemplate.Activities.NewTickets.Adapter  NewTicketAdapter :com.example.standardtemplate.Activities.NewTickets.Adapter  NewTicketDetailsActivity :com.example.standardtemplate.Activities.NewTickets.Adapter  R :com.example.standardtemplate.Activities.NewTickets.Adapter  TicketViewHolder :com.example.standardtemplate.Activities.NewTickets.Adapter  apply :com.example.standardtemplate.Activities.NewTickets.Adapter  java :com.example.standardtemplate.Activities.NewTickets.Adapter  map :com.example.standardtemplate.Activities.NewTickets.Adapter  toSet :com.example.standardtemplate.Activities.NewTickets.Adapter  Context Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  Int Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  Intent Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  LayoutInflater Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  List Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  MutableList Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  NewTicketDetailsActivity Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  NewTicketListResponse Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  R Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  RecyclerView Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  TextView Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  TicketViewHolder Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  View Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  	ViewGroup Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  apply Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  context Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getAPPLY Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getApply Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getMAP Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getMap Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getTOSet Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  getToSet Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  java Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  map Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  notifyDataSetChanged Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  
ticketList Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  toSet Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  
updateData Kcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter  R \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  TextView \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  View \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  itemView \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  txtErrorMsg \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  txtMachineId \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  txtMachineStatus \com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolder  CreateDialog 9com.example.standardtemplate.Activities.NewTickets.Dialog  Date 9com.example.standardtemplate.Activities.NewTickets.Dialog  Dialog 9com.example.standardtemplate.Activities.NewTickets.Dialog  LayoutInflater 9com.example.standardtemplate.Activities.NewTickets.Dialog  Locale 9com.example.standardtemplate.Activities.NewTickets.Dialog  R 9com.example.standardtemplate.Activities.NewTickets.Dialog  SimpleDateFormat 9com.example.standardtemplate.Activities.NewTickets.Dialog  String 9com.example.standardtemplate.Activities.NewTickets.Dialog  TimeZone 9com.example.standardtemplate.Activities.NewTickets.Dialog  UserInfoManager 9com.example.standardtemplate.Activities.NewTickets.Dialog  Window 9com.example.standardtemplate.Activities.NewTickets.Dialog  android 9com.example.standardtemplate.Activities.NewTickets.Dialog  isEmpty 9com.example.standardtemplate.Activities.NewTickets.Dialog  
isNotEmpty 9com.example.standardtemplate.Activities.NewTickets.Dialog  showMessageDialog 9com.example.standardtemplate.Activities.NewTickets.Dialog  toString 9com.example.standardtemplate.Activities.NewTickets.Dialog  Button Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  Context Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  CreateDialogListener Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  Date Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  Dialog Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  EditText Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  LayoutInflater Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  Locale Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  R Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  SimpleDateFormat Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  String Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  TimeZone Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  UserInfoManager Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  Window Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  android Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getANDROID Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getAndroid Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  getCurrentFormattedDate Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getISEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getISNotEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getIsEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
getIsNotEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  getSHOWMessageDialog Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  getShowMessageDialog Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  getTOString Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  getToString Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  isEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  
isNotEmpty Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  listener Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  showCreateDialog Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  showMessageDialog Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  toString Fcom.example.standardtemplate.Activities.NewTickets.Dialog.CreateDialog  CreateDialogListener <com.example.standardtemplate.Activities.NewTickets.Interface  String <com.example.standardtemplate.Activities.NewTickets.Interface  String Qcom.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListener  onSubmitTicket Qcom.example.standardtemplate.Activities.NewTickets.Interface.CreateDialogListener  	ApiClient Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  ApiInterface Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Boolean Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Bundle Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Button Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Call Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Callback Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Log Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  R Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Response Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  String Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  TextView Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	Throwable Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Toast Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Unit Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  UserInfoManager Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  
ViewCompat Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  Void Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  WindowInsetsCompat Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  acceptTicket Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  applicationContext Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	btnAccept Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  btnBack Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  enableEdgeToEdge Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  findViewById Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  finish Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getAPPLICATIONContext Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getApplicationContext Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getENABLEEdgeToEdge Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getEnableEdgeToEdge Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	getINTENT Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  
getISEmpty Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	getIntent Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  
getIsEmpty Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getSHOWMessageDialog Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getShowMessageDialog Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	getString Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getTOInt Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getTOString Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getToInt Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getToString Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  intent Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  isEmpty Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  java Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  onBackPressedDispatcher Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  setApplicationContext Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  setContentView Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	setIntent Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  showMessageDialog Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  toInt Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  toString Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  token Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  txtCreatedAt Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  
txtCreator Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  txtErrorMsg Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  txtMachineDowntime Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  txtMachineId Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  txtMachineStatus Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  	txtStatus Kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity  getAPPLICATIONContext kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  getApplicationContext kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  getGETString kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  getGetString kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  getSHOWMessageDialog kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  getShowMessageDialog kcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivity.acceptTicket.<no name provided>  AcceptedTicketListActivity Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	ApiClient Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  ApiInterface Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Bundle Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Button Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Call Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Callback Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  CreateDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Date Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  GridLayoutManager Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Handler Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
HandlerThread Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Intent Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  List Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Locale Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Log Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
LoginActivity Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  MutableList Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  NewTicketAdapter Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
NewTicketInfo Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  NewTicketListResponse Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  OnBackPressedCallback Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  R Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  RecyclerView Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Response Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
SharedPref Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  SimpleDateFormat Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  String Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	Throwable Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  Toast Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  UserInfoManager Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
ViewCompat Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  WindowInsetsCompat Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
apiService Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  applicationContext Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  apply Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  btnAcceptedTicketList Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	btnCreate Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	btnLogout Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  call_new_ticket_list Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  enableEdgeToEdge Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  fetchNewTicketList Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  findViewById Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  finish Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getAPPLICATIONContext Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getAPPLY Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getApplicationContext Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getApply Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getENABLEEdgeToEdge Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getEnableEdgeToEdge Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
getISNotEmpty Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
getIsNotEmpty Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getLET Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getLet Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getMUTABLEListOf Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getMutableListOf Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getSHOWConfirmDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getSHOWMessageDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getShowConfirmDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getShowMessageDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	getString Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getTOString Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getToString Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  handler Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
handlerThread Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
isNotEmpty Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  java Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  let Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
mutableListOf Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  newTicketAdapter Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
newTicketList Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  onBackPressedDispatcher Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  	retrofit2 Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  rvTicketList Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  setApplicationContext Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  setContentView Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
sharedPref Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  showConfirmDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  showMessageDialog Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  
startActivity Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  toString Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  token Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  username Hcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity  getAPPLICATIONContext ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getApplicationContext ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getCall_new_ticket_list ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getGETString ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getGetString ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  
getHANDLER ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  
getHandler ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getLET ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getLet ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getNEWTicketAdapter ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getNewTicketAdapter ncom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.fetchNewTicketList.<no name provided>  getAPPLICATIONContext dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getAPPLY dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getApplicationContext dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getApply dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  	getFINISH dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  	getFinish dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getGETString dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getGetString dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  
getSHAREDPref dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getSHOWConfirmDialog dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getSTARTActivity dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  
getSharedPref dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getShowConfirmDialog dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getStartActivity dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getUSERNAME dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getUsername dcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onCreate.<no name provided>  getAPPLICATIONContext jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  getApplicationContext jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  getGETString jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  getGetString jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  getSHOWMessageDialog jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  getShowMessageDialog jcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivity.onSubmitTicket.<no name provided>  	ApiClient 0com.example.standardtemplate.Activities.Register  ApiInterface 0com.example.standardtemplate.Activities.Register  Intent 0com.example.standardtemplate.Activities.Register  Log 0com.example.standardtemplate.Activities.Register  
LoginActivity 0com.example.standardtemplate.Activities.Register  R 0com.example.standardtemplate.Activities.Register  RegisterActivity 0com.example.standardtemplate.Activities.Register  RegisterInfo 0com.example.standardtemplate.Activities.Register  String 0com.example.standardtemplate.Activities.Register  	Throwable 0com.example.standardtemplate.Activities.Register  Toast 0com.example.standardtemplate.Activities.Register  
ViewCompat 0com.example.standardtemplate.Activities.Register  WindowInsetsCompat 0com.example.standardtemplate.Activities.Register  applicationContext 0com.example.standardtemplate.Activities.Register  enableEdgeToEdge 0com.example.standardtemplate.Activities.Register  finish 0com.example.standardtemplate.Activities.Register  	getString 0com.example.standardtemplate.Activities.Register  java 0com.example.standardtemplate.Activities.Register  showMessageDialog 0com.example.standardtemplate.Activities.Register  
startActivity 0com.example.standardtemplate.Activities.Register  trim 0com.example.standardtemplate.Activities.Register  	ApiClient Acom.example.standardtemplate.Activities.Register.RegisterActivity  ApiInterface Acom.example.standardtemplate.Activities.Register.RegisterActivity  Bundle Acom.example.standardtemplate.Activities.Register.RegisterActivity  Button Acom.example.standardtemplate.Activities.Register.RegisterActivity  Call Acom.example.standardtemplate.Activities.Register.RegisterActivity  Callback Acom.example.standardtemplate.Activities.Register.RegisterActivity  EditText Acom.example.standardtemplate.Activities.Register.RegisterActivity  Intent Acom.example.standardtemplate.Activities.Register.RegisterActivity  Log Acom.example.standardtemplate.Activities.Register.RegisterActivity  
LoginActivity Acom.example.standardtemplate.Activities.Register.RegisterActivity  R Acom.example.standardtemplate.Activities.Register.RegisterActivity  RegisterInfo Acom.example.standardtemplate.Activities.Register.RegisterActivity  RegisterResponse Acom.example.standardtemplate.Activities.Register.RegisterActivity  Response Acom.example.standardtemplate.Activities.Register.RegisterActivity  String Acom.example.standardtemplate.Activities.Register.RegisterActivity  TextView Acom.example.standardtemplate.Activities.Register.RegisterActivity  	Throwable Acom.example.standardtemplate.Activities.Register.RegisterActivity  Toast Acom.example.standardtemplate.Activities.Register.RegisterActivity  
ViewCompat Acom.example.standardtemplate.Activities.Register.RegisterActivity  WindowInsetsCompat Acom.example.standardtemplate.Activities.Register.RegisterActivity  
apiService Acom.example.standardtemplate.Activities.Register.RegisterActivity  applicationContext Acom.example.standardtemplate.Activities.Register.RegisterActivity  btnRegister Acom.example.standardtemplate.Activities.Register.RegisterActivity  enableEdgeToEdge Acom.example.standardtemplate.Activities.Register.RegisterActivity  findViewById Acom.example.standardtemplate.Activities.Register.RegisterActivity  finish Acom.example.standardtemplate.Activities.Register.RegisterActivity  getAPPLICATIONContext Acom.example.standardtemplate.Activities.Register.RegisterActivity  getApplicationContext Acom.example.standardtemplate.Activities.Register.RegisterActivity  getENABLEEdgeToEdge Acom.example.standardtemplate.Activities.Register.RegisterActivity  getEnableEdgeToEdge Acom.example.standardtemplate.Activities.Register.RegisterActivity  getSHOWMessageDialog Acom.example.standardtemplate.Activities.Register.RegisterActivity  getShowMessageDialog Acom.example.standardtemplate.Activities.Register.RegisterActivity  	getString Acom.example.standardtemplate.Activities.Register.RegisterActivity  getTRIM Acom.example.standardtemplate.Activities.Register.RegisterActivity  getTrim Acom.example.standardtemplate.Activities.Register.RegisterActivity  hlLogin Acom.example.standardtemplate.Activities.Register.RegisterActivity  java Acom.example.standardtemplate.Activities.Register.RegisterActivity  onBackPressedDispatcher Acom.example.standardtemplate.Activities.Register.RegisterActivity  register Acom.example.standardtemplate.Activities.Register.RegisterActivity  setApplicationContext Acom.example.standardtemplate.Activities.Register.RegisterActivity  setContentView Acom.example.standardtemplate.Activities.Register.RegisterActivity  showMessageDialog Acom.example.standardtemplate.Activities.Register.RegisterActivity  
startActivity Acom.example.standardtemplate.Activities.Register.RegisterActivity  trim Acom.example.standardtemplate.Activities.Register.RegisterActivity  txtFullName Acom.example.standardtemplate.Activities.Register.RegisterActivity  txtPassword Acom.example.standardtemplate.Activities.Register.RegisterActivity  txtUsername Acom.example.standardtemplate.Activities.Register.RegisterActivity  getAPPLICATIONContext ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getApplicationContext ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  	getFINISH ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  	getFinish ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getGETString ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getGetString ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getSHOWMessageDialog ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getSTARTActivity ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getShowMessageDialog ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  getStartActivity ]com.example.standardtemplate.Activities.Register.RegisterActivity.register.<no name provided>  ConfirmationDialog $com.example.standardtemplate.Dialogs  Dialog $com.example.standardtemplate.Dialogs  LayoutInflater $com.example.standardtemplate.Dialogs  
MessageDialog $com.example.standardtemplate.Dialogs  R $com.example.standardtemplate.Dialogs  String $com.example.standardtemplate.Dialogs  Unit $com.example.standardtemplate.Dialogs  Window $com.example.standardtemplate.Dialogs  android $com.example.standardtemplate.Dialogs  Button 7com.example.standardtemplate.Dialogs.ConfirmationDialog  Context 7com.example.standardtemplate.Dialogs.ConfirmationDialog  Dialog 7com.example.standardtemplate.Dialogs.ConfirmationDialog  LayoutInflater 7com.example.standardtemplate.Dialogs.ConfirmationDialog  R 7com.example.standardtemplate.Dialogs.ConfirmationDialog  String 7com.example.standardtemplate.Dialogs.ConfirmationDialog  TextView 7com.example.standardtemplate.Dialogs.ConfirmationDialog  Unit 7com.example.standardtemplate.Dialogs.ConfirmationDialog  Window 7com.example.standardtemplate.Dialogs.ConfirmationDialog  android 7com.example.standardtemplate.Dialogs.ConfirmationDialog  
getANDROID 7com.example.standardtemplate.Dialogs.ConfirmationDialog  
getAndroid 7com.example.standardtemplate.Dialogs.ConfirmationDialog  showConfirmDialog 7com.example.standardtemplate.Dialogs.ConfirmationDialog  Button 2com.example.standardtemplate.Dialogs.MessageDialog  Context 2com.example.standardtemplate.Dialogs.MessageDialog  Dialog 2com.example.standardtemplate.Dialogs.MessageDialog  LayoutInflater 2com.example.standardtemplate.Dialogs.MessageDialog  R 2com.example.standardtemplate.Dialogs.MessageDialog  String 2com.example.standardtemplate.Dialogs.MessageDialog  TextView 2com.example.standardtemplate.Dialogs.MessageDialog  Unit 2com.example.standardtemplate.Dialogs.MessageDialog  Window 2com.example.standardtemplate.Dialogs.MessageDialog  android 2com.example.standardtemplate.Dialogs.MessageDialog  
getANDROID 2com.example.standardtemplate.Dialogs.MessageDialog  
getAndroid 2com.example.standardtemplate.Dialogs.MessageDialog  showMessageDialog 2com.example.standardtemplate.Dialogs.MessageDialog  	ApiClient &com.example.standardtemplate.Libraries  ApiInterface &com.example.standardtemplate.Libraries  Build &com.example.standardtemplate.Libraries  CHANNEL_ALERTS &com.example.standardtemplate.Libraries  CHANNEL_SERVICE &com.example.standardtemplate.Libraries  Context &com.example.standardtemplate.Libraries  DEFAULT_POLL_INTERVAL_SEC &com.example.standardtemplate.Libraries  Date &com.example.standardtemplate.Libraries  Dns &com.example.standardtemplate.Libraries  	Exception &com.example.standardtemplate.Libraries  	Executors &com.example.standardtemplate.Libraries  FirebaseMessagingService &com.example.standardtemplate.Libraries  GsonBuilder &com.example.standardtemplate.Libraries  GsonConverterFactory &com.example.standardtemplate.Libraries  Handler &com.example.standardtemplate.Libraries  
HandlerThread &com.example.standardtemplate.Libraries  HttpLoggingInterceptor &com.example.standardtemplate.Libraries  Int &com.example.standardtemplate.Libraries  Intent &com.example.standardtemplate.Libraries  Interceptor &com.example.standardtemplate.Libraries  List &com.example.standardtemplate.Libraries  Locale &com.example.standardtemplate.Libraries  Log &com.example.standardtemplate.Libraries  
LoginActivity &com.example.standardtemplate.Libraries  NewTicketListActivity &com.example.standardtemplate.Libraries  Notification &com.example.standardtemplate.Libraries  NotificationChannel &com.example.standardtemplate.Libraries  NotificationCompat &com.example.standardtemplate.Libraries  NotificationHelper &com.example.standardtemplate.Libraries  NotificationManager &com.example.standardtemplate.Libraries  NotificationManagerCompat &com.example.standardtemplate.Libraries  NotificationService &com.example.standardtemplate.Libraries  OkHttpClient &com.example.standardtemplate.Libraries  
PendingIntent &com.example.standardtemplate.Libraries  R &com.example.standardtemplate.Libraries  Retrofit &com.example.standardtemplate.Libraries  SERVICE_NOTIFICATION_ID &com.example.standardtemplate.Libraries  START_NOT_STICKY &com.example.standardtemplate.Libraries  START_STICKY &com.example.standardtemplate.Libraries  Service &com.example.standardtemplate.Libraries  
SharedPref &com.example.standardtemplate.Libraries  SimpleDateFormat &com.example.standardtemplate.Libraries  String &com.example.standardtemplate.Libraries  System &com.example.standardtemplate.Libraries  	Throwable &com.example.standardtemplate.Libraries  TicketMonitoring &com.example.standardtemplate.Libraries  TimeUnit &com.example.standardtemplate.Libraries  Toast &com.example.standardtemplate.Libraries  UserInfoManager &com.example.standardtemplate.Libraries  Void &com.example.standardtemplate.Libraries  applicationContext &com.example.standardtemplate.Libraries  apply &com.example.standardtemplate.Libraries  	getString &com.example.standardtemplate.Libraries  isBlank &com.example.standardtemplate.Libraries  isEmpty &com.example.standardtemplate.Libraries  java &com.example.standardtemplate.Libraries  
lastDetect &com.example.standardtemplate.Libraries  	lastVisit &com.example.standardtemplate.Libraries  listOf &com.example.standardtemplate.Libraries  showTicketNotification &com.example.standardtemplate.Libraries  toIntOrNull &com.example.standardtemplate.Libraries  toString &com.example.standardtemplate.Libraries  Context 0com.example.standardtemplate.Libraries.ApiClient  Dns 0com.example.standardtemplate.Libraries.ApiClient  Gson 0com.example.standardtemplate.Libraries.ApiClient  GsonBuilder 0com.example.standardtemplate.Libraries.ApiClient  GsonConverterFactory 0com.example.standardtemplate.Libraries.ApiClient  HttpLoggingInterceptor 0com.example.standardtemplate.Libraries.ApiClient  Interceptor 0com.example.standardtemplate.Libraries.ApiClient  OkHttpClient 0com.example.standardtemplate.Libraries.ApiClient  Retrofit 0com.example.standardtemplate.Libraries.ApiClient  
SharedPref 0com.example.standardtemplate.Libraries.ApiClient  String 0com.example.standardtemplate.Libraries.ApiClient  TimeUnit 0com.example.standardtemplate.Libraries.ApiClient  apply 0com.example.standardtemplate.Libraries.ApiClient  authenticatedRetrofit 0com.example.standardtemplate.Libraries.ApiClient  	clearVals 0com.example.standardtemplate.Libraries.ApiClient  getAPPLY 0com.example.standardtemplate.Libraries.ApiClient  getApply 0com.example.standardtemplate.Libraries.ApiClient  getAuthenticatedClient 0com.example.standardtemplate.Libraries.ApiClient  getUnauthenticatedClient 0com.example.standardtemplate.Libraries.ApiClient  invoke 0com.example.standardtemplate.Libraries.ApiClient  
sharedPref 0com.example.standardtemplate.Libraries.ApiClient  unauthenticatedRetrofit 0com.example.standardtemplate.Libraries.ApiClient  AcceptedTicketDto 3com.example.standardtemplate.Libraries.ApiInterface  Body 3com.example.standardtemplate.Libraries.ApiInterface  Call 3com.example.standardtemplate.Libraries.ApiInterface  FcmTokenRequest 3com.example.standardtemplate.Libraries.ApiInterface  GET 3com.example.standardtemplate.Libraries.ApiInterface  Headers 3com.example.standardtemplate.Libraries.ApiInterface  Int 3com.example.standardtemplate.Libraries.ApiInterface  List 3com.example.standardtemplate.Libraries.ApiInterface  Login 3com.example.standardtemplate.Libraries.ApiInterface  	LoginInfo 3com.example.standardtemplate.Libraries.ApiInterface  
LoginResponse 3com.example.standardtemplate.Libraries.ApiInterface  
NewTicketInfo 3com.example.standardtemplate.Libraries.ApiInterface  NewTicketListResponse 3com.example.standardtemplate.Libraries.ApiInterface  POST 3com.example.standardtemplate.Libraries.ApiInterface  Query 3com.example.standardtemplate.Libraries.ApiInterface  RegisterInfo 3com.example.standardtemplate.Libraries.ApiInterface  RegisterResponse 3com.example.standardtemplate.Libraries.ApiInterface  String 3com.example.standardtemplate.Libraries.ApiInterface  
TicketDone 3com.example.standardtemplate.Libraries.ApiInterface  UserDetails 3com.example.standardtemplate.Libraries.ApiInterface  Void 3com.example.standardtemplate.Libraries.ApiInterface  acceptTicket 3com.example.standardtemplate.Libraries.ApiInterface  checkLatestTicket 3com.example.standardtemplate.Libraries.ApiInterface  createNewTicket 3com.example.standardtemplate.Libraries.ApiInterface  getAcceptedTickets 3com.example.standardtemplate.Libraries.ApiInterface  
getTicketList 3com.example.standardtemplate.Libraries.ApiInterface  getUserInfo 3com.example.standardtemplate.Libraries.ApiInterface  registerAccount 3com.example.standardtemplate.Libraries.ApiInterface  	sendToken 3com.example.standardtemplate.Libraries.ApiInterface  solvedTicket 3com.example.standardtemplate.Libraries.ApiInterface  Build ?com.example.standardtemplate.Libraries.FirebaseMessagingService  Context ?com.example.standardtemplate.Libraries.FirebaseMessagingService  Intent ?com.example.standardtemplate.Libraries.FirebaseMessagingService  Log ?com.example.standardtemplate.Libraries.FirebaseMessagingService  
LoginActivity ?com.example.standardtemplate.Libraries.FirebaseMessagingService  NewTicketListActivity ?com.example.standardtemplate.Libraries.FirebaseMessagingService  NotificationChannel ?com.example.standardtemplate.Libraries.FirebaseMessagingService  NotificationCompat ?com.example.standardtemplate.Libraries.FirebaseMessagingService  NotificationManager ?com.example.standardtemplate.Libraries.FirebaseMessagingService  
PendingIntent ?com.example.standardtemplate.Libraries.FirebaseMessagingService  R ?com.example.standardtemplate.Libraries.FirebaseMessagingService  
RemoteMessage ?com.example.standardtemplate.Libraries.FirebaseMessagingService  String ?com.example.standardtemplate.Libraries.FirebaseMessagingService  UserInfoManager ?com.example.standardtemplate.Libraries.FirebaseMessagingService  apply ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getAPPLY ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getApply ?com.example.standardtemplate.Libraries.FirebaseMessagingService  
getISBlank ?com.example.standardtemplate.Libraries.FirebaseMessagingService  
getIsBlank ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getSystemService ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getTOIntOrNull ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getTOString ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getToIntOrNull ?com.example.standardtemplate.Libraries.FirebaseMessagingService  getToString ?com.example.standardtemplate.Libraries.FirebaseMessagingService  isBlank ?com.example.standardtemplate.Libraries.FirebaseMessagingService  java ?com.example.standardtemplate.Libraries.FirebaseMessagingService  sendNotification ?com.example.standardtemplate.Libraries.FirebaseMessagingService  ticketTapIntent ?com.example.standardtemplate.Libraries.FirebaseMessagingService  toIntOrNull ?com.example.standardtemplate.Libraries.FirebaseMessagingService  toString ?com.example.standardtemplate.Libraries.FirebaseMessagingService  	ApiClient 6com.example.standardtemplate.Libraries.LanguageSetting  AppCompatDelegate 6com.example.standardtemplate.Libraries.LanguageSetting  BaseActivity 6com.example.standardtemplate.Libraries.LanguageSetting  
Configuration 6com.example.standardtemplate.Libraries.LanguageSetting  
ContextCompat 6com.example.standardtemplate.Libraries.LanguageSetting  Intent 6com.example.standardtemplate.Libraries.LanguageSetting  Locale 6com.example.standardtemplate.Libraries.LanguageSetting  LocaleListCompat 6com.example.standardtemplate.Libraries.LanguageSetting  Log 6com.example.standardtemplate.Libraries.LanguageSetting  
SharedPref 6com.example.standardtemplate.Libraries.LanguageSetting  StandardTemplate 6com.example.standardtemplate.Libraries.LanguageSetting  String 6com.example.standardtemplate.Libraries.LanguageSetting  TicketMonitoring 6com.example.standardtemplate.Libraries.LanguageSetting  dLocale 6com.example.standardtemplate.Libraries.LanguageSetting  java 6com.example.standardtemplate.Libraries.LanguageSetting  AcceptedTicketListActivity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  ActivityCompat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  AdapterView Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	ApiClient Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  ApiInterface Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  AppCompatDelegate Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  ArrayAdapter Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Boolean Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Build Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Bundle Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Button Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Call Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Callback Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
Configuration Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Context Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
ContextCompat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  CreateDialog Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Date Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  EditText Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  FcmTokenRequest Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  FirebaseMessaging Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  GridLayoutManager Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Handler Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
HandlerThread Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Int Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Intent Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  List Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Locale Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  LocaleListCompat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Log Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
LoginActivity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	LoginInfo Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
LoginResponse Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Long Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Manifest Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Map Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  MutableList Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  NewTicketAdapter Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
NewTicketInfo Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  NewTicketListActivity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  NewTicketListResponse Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  OnBackPressedCallback Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  PackageManager Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  PowerManager Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  R Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  RecyclerView Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  RegisterActivity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  RegisterInfo Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  RegisterResponse Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Response Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Settings Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
SharedPref Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  SimpleDateFormat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Spinner Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  StandardTemplate Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  String Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  SuppressLint Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  TextView Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	Throwable Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  TicketMonitoring Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Toast Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Unit Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Uri Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  UserDetails Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  UserInfoManager Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  View Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
ViewCompat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  Void Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  WindowInsetsCompat Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  acceptTicket Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  applicationContext Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  apply Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  call_new_ticket_list Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  changeLanguage Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  checkPermission Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  dLocale Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  enableEdgeToEdge Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  exitProcess Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  fetchNewTicketList Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  findViewById Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  finish Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  finishAffinity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
getDLocale Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	getString Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  getSystemService Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  getUserData Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  handler Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  isEmpty Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
isNotEmpty Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
isNullOrEmpty Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  java Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	lastVisit Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  let Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  listOf Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  login Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  mapOf Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  maybeRequestBatteryWhitelist Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
mutableListOf Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  newTicketAdapter Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  onCreate Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  onPause Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  onResume Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  recreate Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  register Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  registerFCMToken Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  	retrofit2 Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  sendFcmToken Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  setContentView Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
sharedPref Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  showConfirmDialog Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  showMessageDialog Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  showSettingDialog Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  
startActivity Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  startMonitoringService Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  to Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  toInt Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  toString Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  toTypedArray Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  trim Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  username Ccom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity  AcceptedTicketListActivity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  ActivityCompat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	ApiClient Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  ApiInterface Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  AppCompatDelegate Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  ArrayAdapter Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Build Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
Configuration Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Context Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
ContextCompat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  CreateDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Date Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  FcmTokenRequest Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  FirebaseMessaging Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  GridLayoutManager Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Handler Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
HandlerThread Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Intent Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Locale Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  LocaleListCompat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Log Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
LoginActivity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	LoginInfo Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Manifest Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  NewTicketAdapter Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
NewTicketInfo Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  NewTicketListActivity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  PackageManager Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  R Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  RegisterActivity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  RegisterInfo Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Settings Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
SharedPref Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  SimpleDateFormat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  String Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  TicketMonitoring Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Toast Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  Uri Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  UserInfoManager Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
ViewCompat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  WindowInsetsCompat Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  applicationContext Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  apply Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  call_new_ticket_list Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  changeLanguage Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  dLocale Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  enableEdgeToEdge Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  exitProcess Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  finish Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  finishAffinity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getAPPLY Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getApply Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getEXITProcess Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getExitProcess Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
getISEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
getISNotEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getISNullOrEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
getIsEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
getIsNotEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getIsNullOrEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getLET Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	getLISTOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getLet Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	getListOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getMAPOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getMUTABLEListOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getMapOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getMutableListOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getSHOWConfirmDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getSHOWMessageDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getSHOWSettingDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getShowConfirmDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getShowMessageDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getShowSettingDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	getString Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTO Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTOInt Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTOString Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTOTypedArray Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTRIM Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTo Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getToInt Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getToString Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getToTypedArray Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getTrim Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  getUserData Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  handler Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  isEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
isNotEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
isNullOrEmpty Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  java Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  	lastVisit Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  let Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  listOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  mapOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
mutableListOf Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  newTicketAdapter Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
sharedPref Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  showConfirmDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  showMessageDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  showSettingDialog Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  
startActivity Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  startMonitoringService Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  to Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  toInt Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  toString Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  toTypedArray Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  trim Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  username Mcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivity.Companion  AppCompatDelegate Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  LocaleListCompat Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  Log Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  
SharedPref Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  	setLocale Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  
sharedPref Gcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate  Int /com.example.standardtemplate.Libraries.Managers  String /com.example.standardtemplate.Libraries.Managers  UserInfoManager /com.example.standardtemplate.Libraries.Managers  Date ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  Int ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  
LoginResponse ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  String ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  UserDetails ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  
clearUserData ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  
expiration ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  fullname ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  setAuthentication ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  setUserData ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  token ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  userId ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  username ?com.example.standardtemplate.Libraries.Managers.UserInfoManager  Build 9com.example.standardtemplate.Libraries.NotificationHelper  Context 9com.example.standardtemplate.Libraries.NotificationHelper  Handler 9com.example.standardtemplate.Libraries.NotificationHelper  
HandlerThread 9com.example.standardtemplate.Libraries.NotificationHelper  Intent 9com.example.standardtemplate.Libraries.NotificationHelper  Log 9com.example.standardtemplate.Libraries.NotificationHelper  NewTicketListActivity 9com.example.standardtemplate.Libraries.NotificationHelper  Notification 9com.example.standardtemplate.Libraries.NotificationHelper  NotificationChannel 9com.example.standardtemplate.Libraries.NotificationHelper  NotificationCompat 9com.example.standardtemplate.Libraries.NotificationHelper  NotificationManager 9com.example.standardtemplate.Libraries.NotificationHelper  NotificationManagerCompat 9com.example.standardtemplate.Libraries.NotificationHelper  
PendingIntent 9com.example.standardtemplate.Libraries.NotificationHelper  R 9com.example.standardtemplate.Libraries.NotificationHelper  String 9com.example.standardtemplate.Libraries.NotificationHelper  System 9com.example.standardtemplate.Libraries.NotificationHelper  apply 9com.example.standardtemplate.Libraries.NotificationHelper  	channelId 9com.example.standardtemplate.Libraries.NotificationHelper  context 9com.example.standardtemplate.Libraries.NotificationHelper  createNotification 9com.example.standardtemplate.Libraries.NotificationHelper  createNotificationChannel 9com.example.standardtemplate.Libraries.NotificationHelper  getAPPLY 9com.example.standardtemplate.Libraries.NotificationHelper  getApply 9com.example.standardtemplate.Libraries.NotificationHelper  handler 9com.example.standardtemplate.Libraries.NotificationHelper  
handlerThread 9com.example.standardtemplate.Libraries.NotificationHelper  java 9com.example.standardtemplate.Libraries.NotificationHelper  Build :com.example.standardtemplate.Libraries.NotificationService  IBinder :com.example.standardtemplate.Libraries.NotificationService  Int :com.example.standardtemplate.Libraries.NotificationService  Intent :com.example.standardtemplate.Libraries.NotificationService  Log :com.example.standardtemplate.Libraries.NotificationService  NewTicketListActivity :com.example.standardtemplate.Libraries.NotificationService  Notification :com.example.standardtemplate.Libraries.NotificationService  NotificationChannel :com.example.standardtemplate.Libraries.NotificationService  NotificationCompat :com.example.standardtemplate.Libraries.NotificationService  NotificationManager :com.example.standardtemplate.Libraries.NotificationService  NotificationManagerCompat :com.example.standardtemplate.Libraries.NotificationService  
PendingIntent :com.example.standardtemplate.Libraries.NotificationService  R :com.example.standardtemplate.Libraries.NotificationService  START_NOT_STICKY :com.example.standardtemplate.Libraries.NotificationService  String :com.example.standardtemplate.Libraries.NotificationService  System :com.example.standardtemplate.Libraries.NotificationService  apply :com.example.standardtemplate.Libraries.NotificationService  	channelId :com.example.standardtemplate.Libraries.NotificationService  createForegroundNotification :com.example.standardtemplate.Libraries.NotificationService  createNotification :com.example.standardtemplate.Libraries.NotificationService  createNotificationChannel :com.example.standardtemplate.Libraries.NotificationService  getAPPLY :com.example.standardtemplate.Libraries.NotificationService  getApply :com.example.standardtemplate.Libraries.NotificationService  getSystemService :com.example.standardtemplate.Libraries.NotificationService  java :com.example.standardtemplate.Libraries.NotificationService  sendNotification :com.example.standardtemplate.Libraries.NotificationService  startForeground :com.example.standardtemplate.Libraries.NotificationService  stopSelf :com.example.standardtemplate.Libraries.NotificationService  	ApiClient 7com.example.standardtemplate.Libraries.StandardFunction  Context 7com.example.standardtemplate.Libraries.StandardFunction  Gson 7com.example.standardtemplate.Libraries.StandardFunction  
LoginResponse 7com.example.standardtemplate.Libraries.StandardFunction  
SharedPref 7com.example.standardtemplate.Libraries.StandardFunction  String 7com.example.standardtemplate.Libraries.StandardFunction  UserDetails 7com.example.standardtemplate.Libraries.StandardFunction  java 7com.example.standardtemplate.Libraries.StandardFunction  	ApiClient Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  Context Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  Gson Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  
LoginResponse Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  String Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  UserDetails Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  
getBaseUrl Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  getLanguage Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  getLastVisitRecord Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  java Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  recordLastVisit Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  saveBaseUrl Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  saveLanugage Bcom.example.standardtemplate.Libraries.StandardFunction.SharedPref  	ApiClient 7com.example.standardtemplate.Libraries.TicketMonitoring  ApiInterface 7com.example.standardtemplate.Libraries.TicketMonitoring  Build 7com.example.standardtemplate.Libraries.TicketMonitoring  CHANNEL_ALERTS 7com.example.standardtemplate.Libraries.TicketMonitoring  CHANNEL_SERVICE 7com.example.standardtemplate.Libraries.TicketMonitoring  Call 7com.example.standardtemplate.Libraries.TicketMonitoring  Callback 7com.example.standardtemplate.Libraries.TicketMonitoring  	Companion 7com.example.standardtemplate.Libraries.TicketMonitoring  Context 7com.example.standardtemplate.Libraries.TicketMonitoring  DEFAULT_POLL_INTERVAL_SEC 7com.example.standardtemplate.Libraries.TicketMonitoring  Date 7com.example.standardtemplate.Libraries.TicketMonitoring  	Exception 7com.example.standardtemplate.Libraries.TicketMonitoring  	Executors 7com.example.standardtemplate.Libraries.TicketMonitoring  IBinder 7com.example.standardtemplate.Libraries.TicketMonitoring  Int 7com.example.standardtemplate.Libraries.TicketMonitoring  Intent 7com.example.standardtemplate.Libraries.TicketMonitoring  Locale 7com.example.standardtemplate.Libraries.TicketMonitoring  Log 7com.example.standardtemplate.Libraries.TicketMonitoring  
LoginActivity 7com.example.standardtemplate.Libraries.TicketMonitoring  NewTicketListActivity 7com.example.standardtemplate.Libraries.TicketMonitoring  Notification 7com.example.standardtemplate.Libraries.TicketMonitoring  NotificationChannel 7com.example.standardtemplate.Libraries.TicketMonitoring  NotificationCompat 7com.example.standardtemplate.Libraries.TicketMonitoring  NotificationManager 7com.example.standardtemplate.Libraries.TicketMonitoring  
PendingIntent 7com.example.standardtemplate.Libraries.TicketMonitoring  R 7com.example.standardtemplate.Libraries.TicketMonitoring  Response 7com.example.standardtemplate.Libraries.TicketMonitoring  SERVICE_NOTIFICATION_ID 7com.example.standardtemplate.Libraries.TicketMonitoring  START_STICKY 7com.example.standardtemplate.Libraries.TicketMonitoring  ScheduledExecutorService 7com.example.standardtemplate.Libraries.TicketMonitoring  
SharedPref 7com.example.standardtemplate.Libraries.TicketMonitoring  SimpleDateFormat 7com.example.standardtemplate.Libraries.TicketMonitoring  String 7com.example.standardtemplate.Libraries.TicketMonitoring  System 7com.example.standardtemplate.Libraries.TicketMonitoring  	Throwable 7com.example.standardtemplate.Libraries.TicketMonitoring  TicketMonitoring 7com.example.standardtemplate.Libraries.TicketMonitoring  TimeUnit 7com.example.standardtemplate.Libraries.TicketMonitoring  Toast 7com.example.standardtemplate.Libraries.TicketMonitoring  UserInfoManager 7com.example.standardtemplate.Libraries.TicketMonitoring  applicationContext 7com.example.standardtemplate.Libraries.TicketMonitoring  apply 7com.example.standardtemplate.Libraries.TicketMonitoring  buildServiceNotification 7com.example.standardtemplate.Libraries.TicketMonitoring  checkForNewTickets 7com.example.standardtemplate.Libraries.TicketMonitoring  createNotificationChannels 7com.example.standardtemplate.Libraries.TicketMonitoring  getAPPLICATIONContext 7com.example.standardtemplate.Libraries.TicketMonitoring  getAPPLY 7com.example.standardtemplate.Libraries.TicketMonitoring  getApplicationContext 7com.example.standardtemplate.Libraries.TicketMonitoring  getApply 7com.example.standardtemplate.Libraries.TicketMonitoring  
getISBlank 7com.example.standardtemplate.Libraries.TicketMonitoring  
getISEmpty 7com.example.standardtemplate.Libraries.TicketMonitoring  
getIsBlank 7com.example.standardtemplate.Libraries.TicketMonitoring  
getIsEmpty 7com.example.standardtemplate.Libraries.TicketMonitoring  	getLISTOf 7com.example.standardtemplate.Libraries.TicketMonitoring  	getListOf 7com.example.standardtemplate.Libraries.TicketMonitoring  getPACKAGEName 7com.example.standardtemplate.Libraries.TicketMonitoring  getPackageName 7com.example.standardtemplate.Libraries.TicketMonitoring  	getString 7com.example.standardtemplate.Libraries.TicketMonitoring  getSystemService 7com.example.standardtemplate.Libraries.TicketMonitoring  getTOString 7com.example.standardtemplate.Libraries.TicketMonitoring  getToString 7com.example.standardtemplate.Libraries.TicketMonitoring  isBlank 7com.example.standardtemplate.Libraries.TicketMonitoring  isEmpty 7com.example.standardtemplate.Libraries.TicketMonitoring  java 7com.example.standardtemplate.Libraries.TicketMonitoring  
lastDetect 7com.example.standardtemplate.Libraries.TicketMonitoring  	lastVisit 7com.example.standardtemplate.Libraries.TicketMonitoring  listOf 7com.example.standardtemplate.Libraries.TicketMonitoring  packageName 7com.example.standardtemplate.Libraries.TicketMonitoring  	scheduler 7com.example.standardtemplate.Libraries.TicketMonitoring  setApplicationContext 7com.example.standardtemplate.Libraries.TicketMonitoring  setPackageName 7com.example.standardtemplate.Libraries.TicketMonitoring  
sharedPref 7com.example.standardtemplate.Libraries.TicketMonitoring  showTicketNotification 7com.example.standardtemplate.Libraries.TicketMonitoring  startForeground 7com.example.standardtemplate.Libraries.TicketMonitoring  startPolling 7com.example.standardtemplate.Libraries.TicketMonitoring  ticketTapIntent 7com.example.standardtemplate.Libraries.TicketMonitoring  toString 7com.example.standardtemplate.Libraries.TicketMonitoring  	ApiClient Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  ApiInterface Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Build Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  CHANNEL_ALERTS Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  CHANNEL_SERVICE Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Call Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Callback Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Context Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  DEFAULT_POLL_INTERVAL_SEC Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Date Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	Exception Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	Executors Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  IBinder Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Int Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Intent Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Locale Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Log Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
LoginActivity Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  NewTicketListActivity Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Notification Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  NotificationChannel Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  NotificationCompat Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  NotificationManager Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
PendingIntent Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  R Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Response Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  SERVICE_NOTIFICATION_ID Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  START_STICKY Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  ScheduledExecutorService Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
SharedPref Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  SimpleDateFormat Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  String Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  System Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	Throwable Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  TicketMonitoring Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  TimeUnit Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  Toast Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  UserInfoManager Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  applicationContext Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  apply Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  getAPPLY Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  getApply Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
getISBlank Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
getISEmpty Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
getIsBlank Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
getIsEmpty Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	getLISTOf Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	getListOf Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	getString Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  getTOString Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  getToString Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  isBlank Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  isEmpty Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  java Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  
lastDetect Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  	lastVisit Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  listOf Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  showTicketNotification Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  toString Acom.example.standardtemplate.Libraries.TicketMonitoring.Companion  getAPPLICATIONContext ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getApplicationContext ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getGETString ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getGetString ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  
getLASTDetect ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getLASTVisit ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  
getLastDetect ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getLastVisit ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getSHOWTicketNotification ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  getShowTicketNotification ]com.example.standardtemplate.Libraries.TicketMonitoring.checkForNewTickets.<no name provided>  AcceptedTicketDto #com.example.standardtemplate.Models  Any #com.example.standardtemplate.Models  FcmTokenRequest #com.example.standardtemplate.Models  Int #com.example.standardtemplate.Models  List #com.example.standardtemplate.Models  	LoginInfo #com.example.standardtemplate.Models  
LoginResponse #com.example.standardtemplate.Models  
NewTicketInfo #com.example.standardtemplate.Models  NewTicketListResponse #com.example.standardtemplate.Models  RegisterInfo #com.example.standardtemplate.Models  RegisterResponse #com.example.standardtemplate.Models  String #com.example.standardtemplate.Models  
TicketDone #com.example.standardtemplate.Models  UserDetails #com.example.standardtemplate.Models  UserNavigation #com.example.standardtemplate.Models  Int 5com.example.standardtemplate.Models.AcceptedTicketDto  SerializedName 5com.example.standardtemplate.Models.AcceptedTicketDto  String 5com.example.standardtemplate.Models.AcceptedTicketDto  UserNavigation 5com.example.standardtemplate.Models.AcceptedTicketDto  String 3com.example.standardtemplate.Models.FcmTokenRequest  String -com.example.standardtemplate.Models.LoginInfo  Date 1com.example.standardtemplate.Models.LoginResponse  String 1com.example.standardtemplate.Models.LoginResponse  equals 1com.example.standardtemplate.Models.LoginResponse  
expiration 1com.example.standardtemplate.Models.LoginResponse  token 1com.example.standardtemplate.Models.LoginResponse  SerializedName 1com.example.standardtemplate.Models.NewTicketInfo  String 1com.example.standardtemplate.Models.NewTicketInfo  	machineId 1com.example.standardtemplate.Models.NewTicketInfo  Int 9com.example.standardtemplate.Models.NewTicketListResponse  SerializedName 9com.example.standardtemplate.Models.NewTicketListResponse  String 9com.example.standardtemplate.Models.NewTicketListResponse  	createdAt 9com.example.standardtemplate.Models.NewTicketListResponse  creator 9com.example.standardtemplate.Models.NewTicketListResponse  errorMsg 9com.example.standardtemplate.Models.NewTicketListResponse  id 9com.example.standardtemplate.Models.NewTicketListResponse  machineDowntime 9com.example.standardtemplate.Models.NewTicketListResponse  	machineId 9com.example.standardtemplate.Models.NewTicketListResponse  
machineStatus 9com.example.standardtemplate.Models.NewTicketListResponse  status 9com.example.standardtemplate.Models.NewTicketListResponse  String 0com.example.standardtemplate.Models.RegisterInfo  Any 4com.example.standardtemplate.Models.RegisterResponse  Int 4com.example.standardtemplate.Models.RegisterResponse  List 4com.example.standardtemplate.Models.RegisterResponse  SerializedName 4com.example.standardtemplate.Models.RegisterResponse  String 4com.example.standardtemplate.Models.RegisterResponse  equals 4com.example.standardtemplate.Models.RegisterResponse  Int .com.example.standardtemplate.Models.TicketDone  String .com.example.standardtemplate.Models.TicketDone  Int /com.example.standardtemplate.Models.UserDetails  SerializedName /com.example.standardtemplate.Models.UserDetails  String /com.example.standardtemplate.Models.UserDetails  equals /com.example.standardtemplate.Models.UserDetails  fullname /com.example.standardtemplate.Models.UserDetails  id /com.example.standardtemplate.Models.UserDetails  username /com.example.standardtemplate.Models.UserDetails  Int 2com.example.standardtemplate.Models.UserNavigation  SerializedName 2com.example.standardtemplate.Models.UserNavigation  String 2com.example.standardtemplate.Models.UserNavigation  drawable com.example.standardtemplate.R  id com.example.standardtemplate.R  layout com.example.standardtemplate.R  string com.example.standardtemplate.R  application_icon 'com.example.standardtemplate.R.drawable  ic_launcher_foreground 'com.example.standardtemplate.R.drawable  	btnAccept !com.example.standardtemplate.R.id  btnAcceptedTicketList !com.example.standardtemplate.R.id  btnBack !com.example.standardtemplate.R.id  	btnCancel !com.example.standardtemplate.R.id  btnClose !com.example.standardtemplate.R.id  
btnConfirm !com.example.standardtemplate.R.id  	btnCreate !com.example.standardtemplate.R.id  btnDone !com.example.standardtemplate.R.id  btnLogin !com.example.standardtemplate.R.id  	btnLogout !com.example.standardtemplate.R.id  btnOk !com.example.standardtemplate.R.id  btnRegister !com.example.standardtemplate.R.id  	btnReturn !com.example.standardtemplate.R.id  btnSave !com.example.standardtemplate.R.id  
btnSetting !com.example.standardtemplate.R.id  	btnSubmit !com.example.standardtemplate.R.id  cardView !com.example.standardtemplate.R.id  editErrorMsg !com.example.standardtemplate.R.id  
editMachineId !com.example.standardtemplate.R.id  editMachineStatus !com.example.standardtemplate.R.id  	edtRemedy !com.example.standardtemplate.R.id  edtUrl !com.example.standardtemplate.R.id  hlLogin !com.example.standardtemplate.R.id  
hlRegister !com.example.standardtemplate.R.id  main !com.example.standardtemplate.R.id  rvAcceptedTicketList !com.example.standardtemplate.R.id  rvTicketList !com.example.standardtemplate.R.id  spinner !com.example.standardtemplate.R.id  
tvAttendBy !com.example.standardtemplate.R.id  tvCompleteAt !com.example.standardtemplate.R.id  tvCreatedAt !com.example.standardtemplate.R.id  
tvErrorMsg !com.example.standardtemplate.R.id  tvMachineId !com.example.standardtemplate.R.id  tvMachineStatus !com.example.standardtemplate.R.id  	tvMessage !com.example.standardtemplate.R.id  tvStatus !com.example.standardtemplate.R.id  
tvTicketId !com.example.standardtemplate.R.id  tvTitle !com.example.standardtemplate.R.id  
txtAcceptedby !com.example.standardtemplate.R.id  txtCreatedAt !com.example.standardtemplate.R.id  
txtCreator !com.example.standardtemplate.R.id  txtErrorMsg !com.example.standardtemplate.R.id  txtFullName !com.example.standardtemplate.R.id  txtMachineDowntime !com.example.standardtemplate.R.id  txtMachineId !com.example.standardtemplate.R.id  txtMachineStatus !com.example.standardtemplate.R.id  txtPass !com.example.standardtemplate.R.id  txtPassword !com.example.standardtemplate.R.id  	txtStatus !com.example.standardtemplate.R.id  txtUsername !com.example.standardtemplate.R.id   activity_accepted_ticket_details %com.example.standardtemplate.R.layout  activity_accepted_ticket_list %com.example.standardtemplate.R.layout  activity_login %com.example.standardtemplate.R.layout  activity_new_ticket_details %com.example.standardtemplate.R.layout  activity_new_ticket_list %com.example.standardtemplate.R.layout  activity_register %com.example.standardtemplate.R.layout  dialog_confirmation %com.example.standardtemplate.R.layout  
dialog_create %com.example.standardtemplate.R.layout  dialog_message %com.example.standardtemplate.R.layout  dialog_setting %com.example.standardtemplate.R.layout  item_accepted_ticket_list %com.example.standardtemplate.R.layout  recycle_view_new_ticket_list %com.example.standardtemplate.R.layout  spinner_item %com.example.standardtemplate.R.layout  AT_001 %com.example.standardtemplate.R.string  CT_001 %com.example.standardtemplate.R.string  C_001 %com.example.standardtemplate.R.string  ConnectionError %com.example.standardtemplate.R.string  DT_001 %com.example.standardtemplate.R.string  F_001 %com.example.standardtemplate.R.string  L_001 %com.example.standardtemplate.R.string  L_002 %com.example.standardtemplate.R.string  L_003 %com.example.standardtemplate.R.string  R_001 %com.example.standardtemplate.R.string  U_001 %com.example.standardtemplate.R.string  change_language %com.example.standardtemplate.R.string  change_language_content1 %com.example.standardtemplate.R.string  change_language_content2 %com.example.standardtemplate.R.string  
createSuccess %com.example.standardtemplate.R.string  errorCode_AT_001 %com.example.standardtemplate.R.string  errorCode_CT_001 %com.example.standardtemplate.R.string  errorCode_C_001 %com.example.standardtemplate.R.string  errorCode_DT_001 %com.example.standardtemplate.R.string  errorCode_F_001 %com.example.standardtemplate.R.string  errorCode_L_001 %com.example.standardtemplate.R.string  errorCode_L_002 %com.example.standardtemplate.R.string  errorCode_L_003 %com.example.standardtemplate.R.string  errorCode_R_001 %com.example.standardtemplate.R.string  errorCode_U_001 %com.example.standardtemplate.R.string  exit %com.example.standardtemplate.R.string  exitMsg %com.example.standardtemplate.R.string  failed %com.example.standardtemplate.R.string  	failedMsg %com.example.standardtemplate.R.string  logout %com.example.standardtemplate.R.string  	logoutMsg %com.example.standardtemplate.R.string  
machStatus %com.example.standardtemplate.R.string  proceedLogin %com.example.standardtemplate.R.string  reminder %com.example.standardtemplate.R.string  reminderMsg %com.example.standardtemplate.R.string  reminderMsg2 %com.example.standardtemplate.R.string  success %com.example.standardtemplate.R.string  txtCreatedAt %com.example.standardtemplate.R.string  
txtCreator %com.example.standardtemplate.R.string  txtErrorMsg %com.example.standardtemplate.R.string  txtMachDowntime %com.example.standardtemplate.R.string  	txtStatus %com.example.standardtemplate.R.string  txtmachineId %com.example.standardtemplate.R.string  
updConnection %com.example.standardtemplate.R.string  
updateSuccess %com.example.standardtemplate.R.string  welcMsg %com.example.standardtemplate.R.string  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  addOnCompleteListener !com.google.android.gms.tasks.Task  	exception !com.google.android.gms.tasks.Task  getEXCEPTION !com.google.android.gms.tasks.Task  getException !com.google.android.gms.tasks.Task  getISSuccessful !com.google.android.gms.tasks.Task  getIsSuccessful !com.google.android.gms.tasks.Task  	getRESULT !com.google.android.gms.tasks.Task  	getResult !com.google.android.gms.tasks.Task  isSuccessful !com.google.android.gms.tasks.Task  result !com.google.android.gms.tasks.Task  setException !com.google.android.gms.tasks.Task  	setResult !com.google.android.gms.tasks.Task  
setSuccessful !com.google.android.gms.tasks.Task  FirebaseMessaging com.google.firebase.messaging  FirebaseMessagingService com.google.firebase.messaging  
RemoteMessage com.google.firebase.messaging  Build 3com.google.firebase.messaging.EnhancedIntentService  Context 3com.google.firebase.messaging.EnhancedIntentService  Intent 3com.google.firebase.messaging.EnhancedIntentService  Log 3com.google.firebase.messaging.EnhancedIntentService  
LoginActivity 3com.google.firebase.messaging.EnhancedIntentService  NewTicketListActivity 3com.google.firebase.messaging.EnhancedIntentService  NotificationChannel 3com.google.firebase.messaging.EnhancedIntentService  NotificationCompat 3com.google.firebase.messaging.EnhancedIntentService  NotificationManager 3com.google.firebase.messaging.EnhancedIntentService  
PendingIntent 3com.google.firebase.messaging.EnhancedIntentService  R 3com.google.firebase.messaging.EnhancedIntentService  
RemoteMessage 3com.google.firebase.messaging.EnhancedIntentService  String 3com.google.firebase.messaging.EnhancedIntentService  UserInfoManager 3com.google.firebase.messaging.EnhancedIntentService  apply 3com.google.firebase.messaging.EnhancedIntentService  getSystemService 3com.google.firebase.messaging.EnhancedIntentService  isBlank 3com.google.firebase.messaging.EnhancedIntentService  java 3com.google.firebase.messaging.EnhancedIntentService  
onNewToken 3com.google.firebase.messaging.EnhancedIntentService  sendNotification 3com.google.firebase.messaging.EnhancedIntentService  ticketTapIntent 3com.google.firebase.messaging.EnhancedIntentService  toIntOrNull 3com.google.firebase.messaging.EnhancedIntentService  toString 3com.google.firebase.messaging.EnhancedIntentService  getInstance /com.google.firebase.messaging.FirebaseMessaging  getTOKEN /com.google.firebase.messaging.FirebaseMessaging  getToken /com.google.firebase.messaging.FirebaseMessaging  setToken /com.google.firebase.messaging.FirebaseMessaging  subscribeToTopic /com.google.firebase.messaging.FirebaseMessaging  token /com.google.firebase.messaging.FirebaseMessaging  Build 6com.google.firebase.messaging.FirebaseMessagingService  Context 6com.google.firebase.messaging.FirebaseMessagingService  Intent 6com.google.firebase.messaging.FirebaseMessagingService  Log 6com.google.firebase.messaging.FirebaseMessagingService  
LoginActivity 6com.google.firebase.messaging.FirebaseMessagingService  NewTicketListActivity 6com.google.firebase.messaging.FirebaseMessagingService  NotificationChannel 6com.google.firebase.messaging.FirebaseMessagingService  NotificationCompat 6com.google.firebase.messaging.FirebaseMessagingService  NotificationManager 6com.google.firebase.messaging.FirebaseMessagingService  
PendingIntent 6com.google.firebase.messaging.FirebaseMessagingService  R 6com.google.firebase.messaging.FirebaseMessagingService  
RemoteMessage 6com.google.firebase.messaging.FirebaseMessagingService  String 6com.google.firebase.messaging.FirebaseMessagingService  UserInfoManager 6com.google.firebase.messaging.FirebaseMessagingService  apply 6com.google.firebase.messaging.FirebaseMessagingService  getSystemService 6com.google.firebase.messaging.FirebaseMessagingService  isBlank 6com.google.firebase.messaging.FirebaseMessagingService  java 6com.google.firebase.messaging.FirebaseMessagingService  
onNewToken 6com.google.firebase.messaging.FirebaseMessagingService  sendNotification 6com.google.firebase.messaging.FirebaseMessagingService  ticketTapIntent 6com.google.firebase.messaging.FirebaseMessagingService  toIntOrNull 6com.google.firebase.messaging.FirebaseMessagingService  toString 6com.google.firebase.messaging.FirebaseMessagingService  data +com.google.firebase.messaging.RemoteMessage  getDATA +com.google.firebase.messaging.RemoteMessage  getData +com.google.firebase.messaging.RemoteMessage  getNOTIFICATION +com.google.firebase.messaging.RemoteMessage  getNotification +com.google.firebase.messaging.RemoteMessage  notification +com.google.firebase.messaging.RemoteMessage  setData +com.google.firebase.messaging.RemoteMessage  setNotification +com.google.firebase.messaging.RemoteMessage  body 8com.google.firebase.messaging.RemoteMessage.Notification  getBODY 8com.google.firebase.messaging.RemoteMessage.Notification  getBody 8com.google.firebase.messaging.RemoteMessage.Notification  getTITLE 8com.google.firebase.messaging.RemoteMessage.Notification  getTitle 8com.google.firebase.messaging.RemoteMessage.Notification  setBody 8com.google.firebase.messaging.RemoteMessage.Notification  setTitle 8com.google.firebase.messaging.RemoteMessage.Notification  title 8com.google.firebase.messaging.RemoteMessage.Notification  Gson com.google.gson  GsonBuilder com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  
setLenient com.google.gson.GsonBuilder  SerializedName com.google.gson.annotations  AcceptedTicketAdapter 	java.lang  AcceptedTicketDetailsActivity 	java.lang  AcceptedTicketListActivity 	java.lang  ActivityCompat 	java.lang  	ApiClient 	java.lang  ApiInterface 	java.lang  AppCompatDelegate 	java.lang  ArrayAdapter 	java.lang  Build 	java.lang  CHANNEL_ALERTS 	java.lang  CHANNEL_SERVICE 	java.lang  Class 	java.lang  
Configuration 	java.lang  Context 	java.lang  
ContextCompat 	java.lang  CreateDialog 	java.lang  DEFAULT_POLL_INTERVAL_SEC 	java.lang  Date 	java.lang  Dialog 	java.lang  Dns 	java.lang  	Exception 	java.lang  	Executors 	java.lang  FcmTokenRequest 	java.lang  FirebaseMessaging 	java.lang  GridLayoutManager 	java.lang  Gson 	java.lang  GsonBuilder 	java.lang  GsonConverterFactory 	java.lang  Handler 	java.lang  
HandlerThread 	java.lang  HttpLoggingInterceptor 	java.lang  Intent 	java.lang  Interceptor 	java.lang  LayoutInflater 	java.lang  Locale 	java.lang  LocaleListCompat 	java.lang  Log 	java.lang  
LoginActivity 	java.lang  	LoginInfo 	java.lang  
LoginResponse 	java.lang  Manifest 	java.lang  NewTicketAdapter 	java.lang  NewTicketDetailsActivity 	java.lang  
NewTicketInfo 	java.lang  NewTicketListActivity 	java.lang  NotificationChannel 	java.lang  NotificationCompat 	java.lang  NotificationManager 	java.lang  NotificationManagerCompat 	java.lang  OkHttpClient 	java.lang  PackageManager 	java.lang  
PendingIntent 	java.lang  R 	java.lang  RegisterActivity 	java.lang  RegisterInfo 	java.lang  Retrofit 	java.lang  Runnable 	java.lang  SERVICE_NOTIFICATION_ID 	java.lang  START_NOT_STICKY 	java.lang  START_STICKY 	java.lang  Settings 	java.lang  
SharedPref 	java.lang  SimpleDateFormat 	java.lang  System 	java.lang  
TicketDone 	java.lang  TicketMonitoring 	java.lang  TicketViewHolder 	java.lang  TimeUnit 	java.lang  TimeZone 	java.lang  Toast 	java.lang  Uri 	java.lang  UserDetails 	java.lang  UserInfoManager 	java.lang  
ViewCompat 	java.lang  Void 	java.lang  Window 	java.lang  WindowInsetsCompat 	java.lang  acceptedTicketAdapter 	java.lang  acceptedTicketList 	java.lang  android 	java.lang  applicationContext 	java.lang  apply 	java.lang  call_new_ticket_list 	java.lang  changeLanguage 	java.lang  dLocale 	java.lang  endsWith 	java.lang  exitProcess 	java.lang  finish 	java.lang  finishAffinity 	java.lang  	getString 	java.lang  getUserData 	java.lang  handler 	java.lang  isBlank 	java.lang  isEmpty 	java.lang  
isNotEmpty 	java.lang  
isNullOrEmpty 	java.lang  java 	java.lang  
lastDetect 	java.lang  	lastVisit 	java.lang  let 	java.lang  listOf 	java.lang  map 	java.lang  mapOf 	java.lang  
mutableListOf 	java.lang  newTicketAdapter 	java.lang  onBackPressedDispatcher 	java.lang  	retrofit2 	java.lang  
sharedPref 	java.lang  showConfirmDialog 	java.lang  showMessageDialog 	java.lang  showSettingDialog 	java.lang  showTicketNotification 	java.lang  
startActivity 	java.lang  startMonitoringService 	java.lang  
startsWith 	java.lang  to 	java.lang  toInt 	java.lang  toIntOrNull 	java.lang  toSet 	java.lang  toString 	java.lang  toTypedArray 	java.lang  trim 	java.lang  username 	java.lang  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  currentTimeMillis java.lang.System  apply java.lang.Thread  
quitSafely java.lang.Thread  start java.lang.Thread  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  getTIMEZone java.text.SimpleDateFormat  getTimeZone java.text.SimpleDateFormat  setTimeZone java.text.SimpleDateFormat  timeZone java.text.SimpleDateFormat  	ApiClient 	java.util  ApiInterface 	java.util  Build 	java.util  CHANNEL_ALERTS 	java.util  CHANNEL_SERVICE 	java.util  Context 	java.util  DEFAULT_POLL_INTERVAL_SEC 	java.util  Date 	java.util  	Exception 	java.util  	Executors 	java.util  Intent 	java.util  Locale 	java.util  Log 	java.util  
LoginActivity 	java.util  NewTicketListActivity 	java.util  Notification 	java.util  NotificationChannel 	java.util  NotificationCompat 	java.util  NotificationManager 	java.util  
PendingIntent 	java.util  R 	java.util  SERVICE_NOTIFICATION_ID 	java.util  START_STICKY 	java.util  Service 	java.util  
SharedPref 	java.util  SimpleDateFormat 	java.util  System 	java.util  TicketMonitoring 	java.util  TimeUnit 	java.util  TimeZone 	java.util  Toast 	java.util  UserInfoManager 	java.util  applicationContext 	java.util  apply 	java.util  	getString 	java.util  isBlank 	java.util  isEmpty 	java.util  java 	java.util  
lastDetect 	java.util  	lastVisit 	java.util  listOf 	java.util  showTicketNotification 	java.util  toString 	java.util  
getDefault java.util.Locale  getLANGUAGE java.util.Locale  getLanguage java.util.Locale  language java.util.Locale  setLanguage java.util.Locale  getTimeZone java.util.TimeZone  	Executors java.util.concurrent  ScheduledExecutorService java.util.concurrent  ScheduledFuture java.util.concurrent  TimeUnit java.util.concurrent   newSingleThreadScheduledExecutor java.util.concurrent.Executors  
getISShutdown -java.util.concurrent.ScheduledExecutorService  
getIsShutdown -java.util.concurrent.ScheduledExecutorService  
isShutdown -java.util.concurrent.ScheduledExecutorService  scheduleWithFixedDelay -java.util.concurrent.ScheduledExecutorService  setShutdown -java.util.concurrent.ScheduledExecutorService  shutdownNow -java.util.concurrent.ScheduledExecutorService  SECONDS java.util.concurrent.TimeUnit  AcceptedTicketAdapter kotlin  AcceptedTicketDetailsActivity kotlin  AcceptedTicketListActivity kotlin  ActivityCompat kotlin  Any kotlin  	ApiClient kotlin  ApiInterface kotlin  AppCompatDelegate kotlin  Array kotlin  ArrayAdapter kotlin  Boolean kotlin  Build kotlin  CHANNEL_ALERTS kotlin  CHANNEL_SERVICE kotlin  CharSequence kotlin  
Comparable kotlin  
Configuration kotlin  Context kotlin  
ContextCompat kotlin  CreateDialog kotlin  DEFAULT_POLL_INTERVAL_SEC kotlin  Date kotlin  Dialog kotlin  Dns kotlin  	Exception kotlin  	Executors kotlin  FcmTokenRequest kotlin  FirebaseMessaging kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  GridLayoutManager kotlin  Gson kotlin  GsonBuilder kotlin  GsonConverterFactory kotlin  Handler kotlin  
HandlerThread kotlin  HttpLoggingInterceptor kotlin  Int kotlin  Intent kotlin  Interceptor kotlin  LayoutInflater kotlin  Locale kotlin  LocaleListCompat kotlin  Log kotlin  
LoginActivity kotlin  	LoginInfo kotlin  
LoginResponse kotlin  Long kotlin  Manifest kotlin  NewTicketAdapter kotlin  NewTicketDetailsActivity kotlin  
NewTicketInfo kotlin  NewTicketListActivity kotlin  Nothing kotlin  NotificationChannel kotlin  NotificationCompat kotlin  NotificationManager kotlin  NotificationManagerCompat kotlin  OkHttpClient kotlin  PackageManager kotlin  Pair kotlin  
PendingIntent kotlin  R kotlin  RegisterActivity kotlin  RegisterInfo kotlin  Retrofit kotlin  SERVICE_NOTIFICATION_ID kotlin  START_NOT_STICKY kotlin  START_STICKY kotlin  Settings kotlin  
SharedPref kotlin  SimpleDateFormat kotlin  String kotlin  System kotlin  	Throwable kotlin  
TicketDone kotlin  TicketMonitoring kotlin  TicketViewHolder kotlin  TimeUnit kotlin  TimeZone kotlin  Toast kotlin  Unit kotlin  Uri kotlin  UserDetails kotlin  UserInfoManager kotlin  
ViewCompat kotlin  Void kotlin  Window kotlin  WindowInsetsCompat kotlin  acceptedTicketAdapter kotlin  acceptedTicketList kotlin  android kotlin  applicationContext kotlin  apply kotlin  call_new_ticket_list kotlin  changeLanguage kotlin  dLocale kotlin  endsWith kotlin  exitProcess kotlin  finish kotlin  finishAffinity kotlin  	getString kotlin  getUserData kotlin  handler kotlin  isBlank kotlin  isEmpty kotlin  
isNotEmpty kotlin  
isNullOrEmpty kotlin  java kotlin  
lastDetect kotlin  	lastVisit kotlin  let kotlin  listOf kotlin  map kotlin  mapOf kotlin  
mutableListOf kotlin  newTicketAdapter kotlin  onBackPressedDispatcher kotlin  	retrofit2 kotlin  
sharedPref kotlin  showConfirmDialog kotlin  showMessageDialog kotlin  showSettingDialog kotlin  showTicketNotification kotlin  
startActivity kotlin  startMonitoringService kotlin  
startsWith kotlin  to kotlin  toInt kotlin  toIntOrNull kotlin  toSet kotlin  toString kotlin  toTypedArray kotlin  trim kotlin  username kotlin  getENDSWith 
kotlin.String  getEndsWith 
kotlin.String  
getISBlank 
kotlin.String  
getISEmpty 
kotlin.String  
getISNotEmpty 
kotlin.String  getISNullOrEmpty 
kotlin.String  
getIsBlank 
kotlin.String  
getIsEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  getIsNullOrEmpty 
kotlin.String  
getSTARTSWith 
kotlin.String  
getStartsWith 
kotlin.String  getTO 
kotlin.String  getTOInt 
kotlin.String  getTOIntOrNull 
kotlin.String  getTOString 
kotlin.String  getTRIM 
kotlin.String  getTo 
kotlin.String  getToInt 
kotlin.String  getToIntOrNull 
kotlin.String  getToString 
kotlin.String  getTrim 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  
isNullOrEmpty 
kotlin.String  AcceptedTicketAdapter kotlin.annotation  AcceptedTicketDetailsActivity kotlin.annotation  AcceptedTicketListActivity kotlin.annotation  ActivityCompat kotlin.annotation  	ApiClient kotlin.annotation  ApiInterface kotlin.annotation  AppCompatDelegate kotlin.annotation  ArrayAdapter kotlin.annotation  Build kotlin.annotation  CHANNEL_ALERTS kotlin.annotation  CHANNEL_SERVICE kotlin.annotation  
Configuration kotlin.annotation  Context kotlin.annotation  
ContextCompat kotlin.annotation  CreateDialog kotlin.annotation  DEFAULT_POLL_INTERVAL_SEC kotlin.annotation  Date kotlin.annotation  Dialog kotlin.annotation  Dns kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  FcmTokenRequest kotlin.annotation  FirebaseMessaging kotlin.annotation  GridLayoutManager kotlin.annotation  Gson kotlin.annotation  GsonBuilder kotlin.annotation  GsonConverterFactory kotlin.annotation  Handler kotlin.annotation  
HandlerThread kotlin.annotation  HttpLoggingInterceptor kotlin.annotation  Intent kotlin.annotation  Interceptor kotlin.annotation  LayoutInflater kotlin.annotation  Locale kotlin.annotation  LocaleListCompat kotlin.annotation  Log kotlin.annotation  
LoginActivity kotlin.annotation  	LoginInfo kotlin.annotation  
LoginResponse kotlin.annotation  Manifest kotlin.annotation  NewTicketAdapter kotlin.annotation  NewTicketDetailsActivity kotlin.annotation  
NewTicketInfo kotlin.annotation  NewTicketListActivity kotlin.annotation  NotificationChannel kotlin.annotation  NotificationCompat kotlin.annotation  NotificationManager kotlin.annotation  NotificationManagerCompat kotlin.annotation  OkHttpClient kotlin.annotation  PackageManager kotlin.annotation  
PendingIntent kotlin.annotation  R kotlin.annotation  RegisterActivity kotlin.annotation  RegisterInfo kotlin.annotation  Retrofit kotlin.annotation  SERVICE_NOTIFICATION_ID kotlin.annotation  START_NOT_STICKY kotlin.annotation  START_STICKY kotlin.annotation  Settings kotlin.annotation  
SharedPref kotlin.annotation  SimpleDateFormat kotlin.annotation  System kotlin.annotation  
TicketDone kotlin.annotation  TicketMonitoring kotlin.annotation  TicketViewHolder kotlin.annotation  TimeUnit kotlin.annotation  TimeZone kotlin.annotation  Toast kotlin.annotation  Uri kotlin.annotation  UserDetails kotlin.annotation  UserInfoManager kotlin.annotation  
ViewCompat kotlin.annotation  Void kotlin.annotation  Window kotlin.annotation  WindowInsetsCompat kotlin.annotation  acceptedTicketAdapter kotlin.annotation  acceptedTicketList kotlin.annotation  android kotlin.annotation  applicationContext kotlin.annotation  apply kotlin.annotation  call_new_ticket_list kotlin.annotation  changeLanguage kotlin.annotation  dLocale kotlin.annotation  endsWith kotlin.annotation  exitProcess kotlin.annotation  finish kotlin.annotation  finishAffinity kotlin.annotation  	getString kotlin.annotation  getUserData kotlin.annotation  handler kotlin.annotation  isBlank kotlin.annotation  isEmpty kotlin.annotation  
isNotEmpty kotlin.annotation  
isNullOrEmpty kotlin.annotation  java kotlin.annotation  
lastDetect kotlin.annotation  	lastVisit kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  map kotlin.annotation  mapOf kotlin.annotation  
mutableListOf kotlin.annotation  newTicketAdapter kotlin.annotation  onBackPressedDispatcher kotlin.annotation  	retrofit2 kotlin.annotation  
sharedPref kotlin.annotation  showConfirmDialog kotlin.annotation  showMessageDialog kotlin.annotation  showSettingDialog kotlin.annotation  showTicketNotification kotlin.annotation  
startActivity kotlin.annotation  startMonitoringService kotlin.annotation  
startsWith kotlin.annotation  to kotlin.annotation  toInt kotlin.annotation  toIntOrNull kotlin.annotation  toSet kotlin.annotation  toString kotlin.annotation  toTypedArray kotlin.annotation  trim kotlin.annotation  username kotlin.annotation  AcceptedTicketAdapter kotlin.collections  AcceptedTicketDetailsActivity kotlin.collections  AcceptedTicketListActivity kotlin.collections  ActivityCompat kotlin.collections  	ApiClient kotlin.collections  ApiInterface kotlin.collections  AppCompatDelegate kotlin.collections  ArrayAdapter kotlin.collections  Build kotlin.collections  CHANNEL_ALERTS kotlin.collections  CHANNEL_SERVICE kotlin.collections  
Configuration kotlin.collections  Context kotlin.collections  
ContextCompat kotlin.collections  CreateDialog kotlin.collections  DEFAULT_POLL_INTERVAL_SEC kotlin.collections  Date kotlin.collections  Dialog kotlin.collections  Dns kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  FcmTokenRequest kotlin.collections  FirebaseMessaging kotlin.collections  GridLayoutManager kotlin.collections  Gson kotlin.collections  GsonBuilder kotlin.collections  GsonConverterFactory kotlin.collections  Handler kotlin.collections  
HandlerThread kotlin.collections  HttpLoggingInterceptor kotlin.collections  Intent kotlin.collections  Interceptor kotlin.collections  LayoutInflater kotlin.collections  List kotlin.collections  Locale kotlin.collections  LocaleListCompat kotlin.collections  Log kotlin.collections  
LoginActivity kotlin.collections  	LoginInfo kotlin.collections  
LoginResponse kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  NewTicketAdapter kotlin.collections  NewTicketDetailsActivity kotlin.collections  
NewTicketInfo kotlin.collections  NewTicketListActivity kotlin.collections  NotificationChannel kotlin.collections  NotificationCompat kotlin.collections  NotificationManager kotlin.collections  NotificationManagerCompat kotlin.collections  OkHttpClient kotlin.collections  PackageManager kotlin.collections  
PendingIntent kotlin.collections  R kotlin.collections  RegisterActivity kotlin.collections  RegisterInfo kotlin.collections  Retrofit kotlin.collections  SERVICE_NOTIFICATION_ID kotlin.collections  START_NOT_STICKY kotlin.collections  START_STICKY kotlin.collections  Set kotlin.collections  Settings kotlin.collections  
SharedPref kotlin.collections  SimpleDateFormat kotlin.collections  System kotlin.collections  
TicketDone kotlin.collections  TicketMonitoring kotlin.collections  TicketViewHolder kotlin.collections  TimeUnit kotlin.collections  TimeZone kotlin.collections  Toast kotlin.collections  Uri kotlin.collections  UserDetails kotlin.collections  UserInfoManager kotlin.collections  
ViewCompat kotlin.collections  Void kotlin.collections  Window kotlin.collections  WindowInsetsCompat kotlin.collections  acceptedTicketAdapter kotlin.collections  acceptedTicketList kotlin.collections  android kotlin.collections  applicationContext kotlin.collections  apply kotlin.collections  call_new_ticket_list kotlin.collections  changeLanguage kotlin.collections  dLocale kotlin.collections  endsWith kotlin.collections  exitProcess kotlin.collections  finish kotlin.collections  finishAffinity kotlin.collections  	getString kotlin.collections  getUserData kotlin.collections  handler kotlin.collections  isBlank kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  
isNullOrEmpty kotlin.collections  java kotlin.collections  
lastDetect kotlin.collections  	lastVisit kotlin.collections  let kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  newTicketAdapter kotlin.collections  onBackPressedDispatcher kotlin.collections  	retrofit2 kotlin.collections  
sharedPref kotlin.collections  showConfirmDialog kotlin.collections  showMessageDialog kotlin.collections  showSettingDialog kotlin.collections  showTicketNotification kotlin.collections  
startActivity kotlin.collections  startMonitoringService kotlin.collections  
startsWith kotlin.collections  to kotlin.collections  toInt kotlin.collections  toIntOrNull kotlin.collections  toSet kotlin.collections  toString kotlin.collections  toTypedArray kotlin.collections  trim kotlin.collections  username kotlin.collections  getLET kotlin.collections.List  getLet kotlin.collections.List  getMAP kotlin.collections.List  getMap kotlin.collections.List  getTOSet kotlin.collections.List  getToSet kotlin.collections.List  
getISNotEmpty kotlin.collections.MutableList  
getIsNotEmpty kotlin.collections.MutableList  getMAP kotlin.collections.MutableList  getMap kotlin.collections.MutableList  getTOTypedArray kotlin.collections.MutableList  getToTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  AcceptedTicketAdapter kotlin.comparisons  AcceptedTicketDetailsActivity kotlin.comparisons  AcceptedTicketListActivity kotlin.comparisons  ActivityCompat kotlin.comparisons  	ApiClient kotlin.comparisons  ApiInterface kotlin.comparisons  AppCompatDelegate kotlin.comparisons  ArrayAdapter kotlin.comparisons  Build kotlin.comparisons  CHANNEL_ALERTS kotlin.comparisons  CHANNEL_SERVICE kotlin.comparisons  
Configuration kotlin.comparisons  Context kotlin.comparisons  
ContextCompat kotlin.comparisons  CreateDialog kotlin.comparisons  DEFAULT_POLL_INTERVAL_SEC kotlin.comparisons  Date kotlin.comparisons  Dialog kotlin.comparisons  Dns kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  FcmTokenRequest kotlin.comparisons  FirebaseMessaging kotlin.comparisons  GridLayoutManager kotlin.comparisons  Gson kotlin.comparisons  GsonBuilder kotlin.comparisons  GsonConverterFactory kotlin.comparisons  Handler kotlin.comparisons  
HandlerThread kotlin.comparisons  HttpLoggingInterceptor kotlin.comparisons  Intent kotlin.comparisons  Interceptor kotlin.comparisons  LayoutInflater kotlin.comparisons  Locale kotlin.comparisons  LocaleListCompat kotlin.comparisons  Log kotlin.comparisons  
LoginActivity kotlin.comparisons  	LoginInfo kotlin.comparisons  
LoginResponse kotlin.comparisons  Manifest kotlin.comparisons  NewTicketAdapter kotlin.comparisons  NewTicketDetailsActivity kotlin.comparisons  
NewTicketInfo kotlin.comparisons  NewTicketListActivity kotlin.comparisons  NotificationChannel kotlin.comparisons  NotificationCompat kotlin.comparisons  NotificationManager kotlin.comparisons  NotificationManagerCompat kotlin.comparisons  OkHttpClient kotlin.comparisons  PackageManager kotlin.comparisons  
PendingIntent kotlin.comparisons  R kotlin.comparisons  RegisterActivity kotlin.comparisons  RegisterInfo kotlin.comparisons  Retrofit kotlin.comparisons  SERVICE_NOTIFICATION_ID kotlin.comparisons  START_NOT_STICKY kotlin.comparisons  START_STICKY kotlin.comparisons  Settings kotlin.comparisons  
SharedPref kotlin.comparisons  SimpleDateFormat kotlin.comparisons  System kotlin.comparisons  
TicketDone kotlin.comparisons  TicketMonitoring kotlin.comparisons  TicketViewHolder kotlin.comparisons  TimeUnit kotlin.comparisons  TimeZone kotlin.comparisons  Toast kotlin.comparisons  Uri kotlin.comparisons  UserDetails kotlin.comparisons  UserInfoManager kotlin.comparisons  
ViewCompat kotlin.comparisons  Void kotlin.comparisons  Window kotlin.comparisons  WindowInsetsCompat kotlin.comparisons  acceptedTicketAdapter kotlin.comparisons  acceptedTicketList kotlin.comparisons  android kotlin.comparisons  applicationContext kotlin.comparisons  apply kotlin.comparisons  call_new_ticket_list kotlin.comparisons  changeLanguage kotlin.comparisons  dLocale kotlin.comparisons  endsWith kotlin.comparisons  exitProcess kotlin.comparisons  finish kotlin.comparisons  finishAffinity kotlin.comparisons  	getString kotlin.comparisons  getUserData kotlin.comparisons  handler kotlin.comparisons  isBlank kotlin.comparisons  isEmpty kotlin.comparisons  
isNotEmpty kotlin.comparisons  
isNullOrEmpty kotlin.comparisons  java kotlin.comparisons  
lastDetect kotlin.comparisons  	lastVisit kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  map kotlin.comparisons  mapOf kotlin.comparisons  
mutableListOf kotlin.comparisons  newTicketAdapter kotlin.comparisons  onBackPressedDispatcher kotlin.comparisons  	retrofit2 kotlin.comparisons  
sharedPref kotlin.comparisons  showConfirmDialog kotlin.comparisons  showMessageDialog kotlin.comparisons  showSettingDialog kotlin.comparisons  showTicketNotification kotlin.comparisons  
startActivity kotlin.comparisons  startMonitoringService kotlin.comparisons  
startsWith kotlin.comparisons  to kotlin.comparisons  toInt kotlin.comparisons  toIntOrNull kotlin.comparisons  toSet kotlin.comparisons  toString kotlin.comparisons  toTypedArray kotlin.comparisons  trim kotlin.comparisons  username kotlin.comparisons  AcceptedTicketAdapter 	kotlin.io  AcceptedTicketDetailsActivity 	kotlin.io  AcceptedTicketListActivity 	kotlin.io  ActivityCompat 	kotlin.io  	ApiClient 	kotlin.io  ApiInterface 	kotlin.io  AppCompatDelegate 	kotlin.io  ArrayAdapter 	kotlin.io  Build 	kotlin.io  CHANNEL_ALERTS 	kotlin.io  CHANNEL_SERVICE 	kotlin.io  
Configuration 	kotlin.io  Context 	kotlin.io  
ContextCompat 	kotlin.io  CreateDialog 	kotlin.io  DEFAULT_POLL_INTERVAL_SEC 	kotlin.io  Date 	kotlin.io  Dialog 	kotlin.io  Dns 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  FcmTokenRequest 	kotlin.io  FirebaseMessaging 	kotlin.io  GridLayoutManager 	kotlin.io  Gson 	kotlin.io  GsonBuilder 	kotlin.io  GsonConverterFactory 	kotlin.io  Handler 	kotlin.io  
HandlerThread 	kotlin.io  HttpLoggingInterceptor 	kotlin.io  Intent 	kotlin.io  Interceptor 	kotlin.io  LayoutInflater 	kotlin.io  Locale 	kotlin.io  LocaleListCompat 	kotlin.io  Log 	kotlin.io  
LoginActivity 	kotlin.io  	LoginInfo 	kotlin.io  
LoginResponse 	kotlin.io  Manifest 	kotlin.io  NewTicketAdapter 	kotlin.io  NewTicketDetailsActivity 	kotlin.io  
NewTicketInfo 	kotlin.io  NewTicketListActivity 	kotlin.io  NotificationChannel 	kotlin.io  NotificationCompat 	kotlin.io  NotificationManager 	kotlin.io  NotificationManagerCompat 	kotlin.io  OkHttpClient 	kotlin.io  PackageManager 	kotlin.io  
PendingIntent 	kotlin.io  R 	kotlin.io  RegisterActivity 	kotlin.io  RegisterInfo 	kotlin.io  Retrofit 	kotlin.io  SERVICE_NOTIFICATION_ID 	kotlin.io  START_NOT_STICKY 	kotlin.io  START_STICKY 	kotlin.io  Settings 	kotlin.io  
SharedPref 	kotlin.io  SimpleDateFormat 	kotlin.io  System 	kotlin.io  
TicketDone 	kotlin.io  TicketMonitoring 	kotlin.io  TicketViewHolder 	kotlin.io  TimeUnit 	kotlin.io  TimeZone 	kotlin.io  Toast 	kotlin.io  Uri 	kotlin.io  UserDetails 	kotlin.io  UserInfoManager 	kotlin.io  
ViewCompat 	kotlin.io  Void 	kotlin.io  Window 	kotlin.io  WindowInsetsCompat 	kotlin.io  acceptedTicketAdapter 	kotlin.io  acceptedTicketList 	kotlin.io  android 	kotlin.io  applicationContext 	kotlin.io  apply 	kotlin.io  call_new_ticket_list 	kotlin.io  changeLanguage 	kotlin.io  dLocale 	kotlin.io  endsWith 	kotlin.io  exitProcess 	kotlin.io  finish 	kotlin.io  finishAffinity 	kotlin.io  	getString 	kotlin.io  getUserData 	kotlin.io  handler 	kotlin.io  isBlank 	kotlin.io  isEmpty 	kotlin.io  
isNotEmpty 	kotlin.io  
isNullOrEmpty 	kotlin.io  java 	kotlin.io  
lastDetect 	kotlin.io  	lastVisit 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  map 	kotlin.io  mapOf 	kotlin.io  
mutableListOf 	kotlin.io  newTicketAdapter 	kotlin.io  onBackPressedDispatcher 	kotlin.io  	retrofit2 	kotlin.io  
sharedPref 	kotlin.io  showConfirmDialog 	kotlin.io  showMessageDialog 	kotlin.io  showSettingDialog 	kotlin.io  showTicketNotification 	kotlin.io  
startActivity 	kotlin.io  startMonitoringService 	kotlin.io  
startsWith 	kotlin.io  to 	kotlin.io  toInt 	kotlin.io  toIntOrNull 	kotlin.io  toSet 	kotlin.io  toString 	kotlin.io  toTypedArray 	kotlin.io  trim 	kotlin.io  username 	kotlin.io  AcceptedTicketAdapter 
kotlin.jvm  AcceptedTicketDetailsActivity 
kotlin.jvm  AcceptedTicketListActivity 
kotlin.jvm  ActivityCompat 
kotlin.jvm  	ApiClient 
kotlin.jvm  ApiInterface 
kotlin.jvm  AppCompatDelegate 
kotlin.jvm  ArrayAdapter 
kotlin.jvm  Build 
kotlin.jvm  CHANNEL_ALERTS 
kotlin.jvm  CHANNEL_SERVICE 
kotlin.jvm  
Configuration 
kotlin.jvm  Context 
kotlin.jvm  
ContextCompat 
kotlin.jvm  CreateDialog 
kotlin.jvm  DEFAULT_POLL_INTERVAL_SEC 
kotlin.jvm  Date 
kotlin.jvm  Dialog 
kotlin.jvm  Dns 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  FcmTokenRequest 
kotlin.jvm  FirebaseMessaging 
kotlin.jvm  GridLayoutManager 
kotlin.jvm  Gson 
kotlin.jvm  GsonBuilder 
kotlin.jvm  GsonConverterFactory 
kotlin.jvm  Handler 
kotlin.jvm  
HandlerThread 
kotlin.jvm  HttpLoggingInterceptor 
kotlin.jvm  Intent 
kotlin.jvm  Interceptor 
kotlin.jvm  LayoutInflater 
kotlin.jvm  Locale 
kotlin.jvm  LocaleListCompat 
kotlin.jvm  Log 
kotlin.jvm  
LoginActivity 
kotlin.jvm  	LoginInfo 
kotlin.jvm  
LoginResponse 
kotlin.jvm  Manifest 
kotlin.jvm  NewTicketAdapter 
kotlin.jvm  NewTicketDetailsActivity 
kotlin.jvm  
NewTicketInfo 
kotlin.jvm  NewTicketListActivity 
kotlin.jvm  NotificationChannel 
kotlin.jvm  NotificationCompat 
kotlin.jvm  NotificationManager 
kotlin.jvm  NotificationManagerCompat 
kotlin.jvm  OkHttpClient 
kotlin.jvm  PackageManager 
kotlin.jvm  
PendingIntent 
kotlin.jvm  R 
kotlin.jvm  RegisterActivity 
kotlin.jvm  RegisterInfo 
kotlin.jvm  Retrofit 
kotlin.jvm  SERVICE_NOTIFICATION_ID 
kotlin.jvm  START_NOT_STICKY 
kotlin.jvm  START_STICKY 
kotlin.jvm  Settings 
kotlin.jvm  
SharedPref 
kotlin.jvm  SimpleDateFormat 
kotlin.jvm  System 
kotlin.jvm  
TicketDone 
kotlin.jvm  TicketMonitoring 
kotlin.jvm  TicketViewHolder 
kotlin.jvm  TimeUnit 
kotlin.jvm  TimeZone 
kotlin.jvm  Toast 
kotlin.jvm  Uri 
kotlin.jvm  UserDetails 
kotlin.jvm  UserInfoManager 
kotlin.jvm  
ViewCompat 
kotlin.jvm  Void 
kotlin.jvm  Window 
kotlin.jvm  WindowInsetsCompat 
kotlin.jvm  acceptedTicketAdapter 
kotlin.jvm  acceptedTicketList 
kotlin.jvm  android 
kotlin.jvm  applicationContext 
kotlin.jvm  apply 
kotlin.jvm  call_new_ticket_list 
kotlin.jvm  changeLanguage 
kotlin.jvm  dLocale 
kotlin.jvm  endsWith 
kotlin.jvm  exitProcess 
kotlin.jvm  finish 
kotlin.jvm  finishAffinity 
kotlin.jvm  	getString 
kotlin.jvm  getUserData 
kotlin.jvm  handler 
kotlin.jvm  isBlank 
kotlin.jvm  isEmpty 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  
isNullOrEmpty 
kotlin.jvm  java 
kotlin.jvm  
lastDetect 
kotlin.jvm  	lastVisit 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  map 
kotlin.jvm  mapOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  newTicketAdapter 
kotlin.jvm  onBackPressedDispatcher 
kotlin.jvm  	retrofit2 
kotlin.jvm  
sharedPref 
kotlin.jvm  showConfirmDialog 
kotlin.jvm  showMessageDialog 
kotlin.jvm  showSettingDialog 
kotlin.jvm  showTicketNotification 
kotlin.jvm  
startActivity 
kotlin.jvm  startMonitoringService 
kotlin.jvm  
startsWith 
kotlin.jvm  to 
kotlin.jvm  toInt 
kotlin.jvm  toIntOrNull 
kotlin.jvm  toSet 
kotlin.jvm  toString 
kotlin.jvm  toTypedArray 
kotlin.jvm  trim 
kotlin.jvm  username 
kotlin.jvm  AcceptedTicketAdapter 
kotlin.ranges  AcceptedTicketDetailsActivity 
kotlin.ranges  AcceptedTicketListActivity 
kotlin.ranges  ActivityCompat 
kotlin.ranges  	ApiClient 
kotlin.ranges  ApiInterface 
kotlin.ranges  AppCompatDelegate 
kotlin.ranges  ArrayAdapter 
kotlin.ranges  Build 
kotlin.ranges  CHANNEL_ALERTS 
kotlin.ranges  CHANNEL_SERVICE 
kotlin.ranges  
Configuration 
kotlin.ranges  Context 
kotlin.ranges  
ContextCompat 
kotlin.ranges  CreateDialog 
kotlin.ranges  DEFAULT_POLL_INTERVAL_SEC 
kotlin.ranges  Date 
kotlin.ranges  Dialog 
kotlin.ranges  Dns 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  FcmTokenRequest 
kotlin.ranges  FirebaseMessaging 
kotlin.ranges  GridLayoutManager 
kotlin.ranges  Gson 
kotlin.ranges  GsonBuilder 
kotlin.ranges  GsonConverterFactory 
kotlin.ranges  Handler 
kotlin.ranges  
HandlerThread 
kotlin.ranges  HttpLoggingInterceptor 
kotlin.ranges  Intent 
kotlin.ranges  Interceptor 
kotlin.ranges  LayoutInflater 
kotlin.ranges  Locale 
kotlin.ranges  LocaleListCompat 
kotlin.ranges  Log 
kotlin.ranges  
LoginActivity 
kotlin.ranges  	LoginInfo 
kotlin.ranges  
LoginResponse 
kotlin.ranges  Manifest 
kotlin.ranges  NewTicketAdapter 
kotlin.ranges  NewTicketDetailsActivity 
kotlin.ranges  
NewTicketInfo 
kotlin.ranges  NewTicketListActivity 
kotlin.ranges  NotificationChannel 
kotlin.ranges  NotificationCompat 
kotlin.ranges  NotificationManager 
kotlin.ranges  NotificationManagerCompat 
kotlin.ranges  OkHttpClient 
kotlin.ranges  PackageManager 
kotlin.ranges  
PendingIntent 
kotlin.ranges  R 
kotlin.ranges  RegisterActivity 
kotlin.ranges  RegisterInfo 
kotlin.ranges  Retrofit 
kotlin.ranges  SERVICE_NOTIFICATION_ID 
kotlin.ranges  START_NOT_STICKY 
kotlin.ranges  START_STICKY 
kotlin.ranges  Settings 
kotlin.ranges  
SharedPref 
kotlin.ranges  SimpleDateFormat 
kotlin.ranges  System 
kotlin.ranges  
TicketDone 
kotlin.ranges  TicketMonitoring 
kotlin.ranges  TicketViewHolder 
kotlin.ranges  TimeUnit 
kotlin.ranges  TimeZone 
kotlin.ranges  Toast 
kotlin.ranges  Uri 
kotlin.ranges  UserDetails 
kotlin.ranges  UserInfoManager 
kotlin.ranges  
ViewCompat 
kotlin.ranges  Void 
kotlin.ranges  Window 
kotlin.ranges  WindowInsetsCompat 
kotlin.ranges  acceptedTicketAdapter 
kotlin.ranges  acceptedTicketList 
kotlin.ranges  android 
kotlin.ranges  applicationContext 
kotlin.ranges  apply 
kotlin.ranges  call_new_ticket_list 
kotlin.ranges  changeLanguage 
kotlin.ranges  dLocale 
kotlin.ranges  endsWith 
kotlin.ranges  exitProcess 
kotlin.ranges  finish 
kotlin.ranges  finishAffinity 
kotlin.ranges  	getString 
kotlin.ranges  getUserData 
kotlin.ranges  handler 
kotlin.ranges  isBlank 
kotlin.ranges  isEmpty 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  
isNullOrEmpty 
kotlin.ranges  java 
kotlin.ranges  
lastDetect 
kotlin.ranges  	lastVisit 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  map 
kotlin.ranges  mapOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  newTicketAdapter 
kotlin.ranges  onBackPressedDispatcher 
kotlin.ranges  	retrofit2 
kotlin.ranges  
sharedPref 
kotlin.ranges  showConfirmDialog 
kotlin.ranges  showMessageDialog 
kotlin.ranges  showSettingDialog 
kotlin.ranges  showTicketNotification 
kotlin.ranges  
startActivity 
kotlin.ranges  startMonitoringService 
kotlin.ranges  
startsWith 
kotlin.ranges  to 
kotlin.ranges  toInt 
kotlin.ranges  toIntOrNull 
kotlin.ranges  toSet 
kotlin.ranges  toString 
kotlin.ranges  toTypedArray 
kotlin.ranges  trim 
kotlin.ranges  username 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AcceptedTicketAdapter kotlin.sequences  AcceptedTicketDetailsActivity kotlin.sequences  AcceptedTicketListActivity kotlin.sequences  ActivityCompat kotlin.sequences  	ApiClient kotlin.sequences  ApiInterface kotlin.sequences  AppCompatDelegate kotlin.sequences  ArrayAdapter kotlin.sequences  Build kotlin.sequences  CHANNEL_ALERTS kotlin.sequences  CHANNEL_SERVICE kotlin.sequences  
Configuration kotlin.sequences  Context kotlin.sequences  
ContextCompat kotlin.sequences  CreateDialog kotlin.sequences  DEFAULT_POLL_INTERVAL_SEC kotlin.sequences  Date kotlin.sequences  Dialog kotlin.sequences  Dns kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  FcmTokenRequest kotlin.sequences  FirebaseMessaging kotlin.sequences  GridLayoutManager kotlin.sequences  Gson kotlin.sequences  GsonBuilder kotlin.sequences  GsonConverterFactory kotlin.sequences  Handler kotlin.sequences  
HandlerThread kotlin.sequences  HttpLoggingInterceptor kotlin.sequences  Intent kotlin.sequences  Interceptor kotlin.sequences  LayoutInflater kotlin.sequences  Locale kotlin.sequences  LocaleListCompat kotlin.sequences  Log kotlin.sequences  
LoginActivity kotlin.sequences  	LoginInfo kotlin.sequences  
LoginResponse kotlin.sequences  Manifest kotlin.sequences  NewTicketAdapter kotlin.sequences  NewTicketDetailsActivity kotlin.sequences  
NewTicketInfo kotlin.sequences  NewTicketListActivity kotlin.sequences  NotificationChannel kotlin.sequences  NotificationCompat kotlin.sequences  NotificationManager kotlin.sequences  NotificationManagerCompat kotlin.sequences  OkHttpClient kotlin.sequences  PackageManager kotlin.sequences  
PendingIntent kotlin.sequences  R kotlin.sequences  RegisterActivity kotlin.sequences  RegisterInfo kotlin.sequences  Retrofit kotlin.sequences  SERVICE_NOTIFICATION_ID kotlin.sequences  START_NOT_STICKY kotlin.sequences  START_STICKY kotlin.sequences  Settings kotlin.sequences  
SharedPref kotlin.sequences  SimpleDateFormat kotlin.sequences  System kotlin.sequences  
TicketDone kotlin.sequences  TicketMonitoring kotlin.sequences  TicketViewHolder kotlin.sequences  TimeUnit kotlin.sequences  TimeZone kotlin.sequences  Toast kotlin.sequences  Uri kotlin.sequences  UserDetails kotlin.sequences  UserInfoManager kotlin.sequences  
ViewCompat kotlin.sequences  Void kotlin.sequences  Window kotlin.sequences  WindowInsetsCompat kotlin.sequences  acceptedTicketAdapter kotlin.sequences  acceptedTicketList kotlin.sequences  android kotlin.sequences  applicationContext kotlin.sequences  apply kotlin.sequences  call_new_ticket_list kotlin.sequences  changeLanguage kotlin.sequences  dLocale kotlin.sequences  endsWith kotlin.sequences  exitProcess kotlin.sequences  finish kotlin.sequences  finishAffinity kotlin.sequences  	getString kotlin.sequences  getUserData kotlin.sequences  handler kotlin.sequences  isBlank kotlin.sequences  isEmpty kotlin.sequences  
isNotEmpty kotlin.sequences  
isNullOrEmpty kotlin.sequences  java kotlin.sequences  
lastDetect kotlin.sequences  	lastVisit kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  map kotlin.sequences  mapOf kotlin.sequences  
mutableListOf kotlin.sequences  newTicketAdapter kotlin.sequences  onBackPressedDispatcher kotlin.sequences  	retrofit2 kotlin.sequences  
sharedPref kotlin.sequences  showConfirmDialog kotlin.sequences  showMessageDialog kotlin.sequences  showSettingDialog kotlin.sequences  showTicketNotification kotlin.sequences  
startActivity kotlin.sequences  startMonitoringService kotlin.sequences  
startsWith kotlin.sequences  to kotlin.sequences  toInt kotlin.sequences  toIntOrNull kotlin.sequences  toSet kotlin.sequences  toString kotlin.sequences  toTypedArray kotlin.sequences  trim kotlin.sequences  username kotlin.sequences  exitProcess 
kotlin.system  AcceptedTicketAdapter kotlin.text  AcceptedTicketDetailsActivity kotlin.text  AcceptedTicketListActivity kotlin.text  ActivityCompat kotlin.text  	ApiClient kotlin.text  ApiInterface kotlin.text  AppCompatDelegate kotlin.text  ArrayAdapter kotlin.text  Build kotlin.text  CHANNEL_ALERTS kotlin.text  CHANNEL_SERVICE kotlin.text  
Configuration kotlin.text  Context kotlin.text  
ContextCompat kotlin.text  CreateDialog kotlin.text  DEFAULT_POLL_INTERVAL_SEC kotlin.text  Date kotlin.text  Dialog kotlin.text  Dns kotlin.text  	Exception kotlin.text  	Executors kotlin.text  FcmTokenRequest kotlin.text  FirebaseMessaging kotlin.text  GridLayoutManager kotlin.text  Gson kotlin.text  GsonBuilder kotlin.text  GsonConverterFactory kotlin.text  Handler kotlin.text  
HandlerThread kotlin.text  HttpLoggingInterceptor kotlin.text  Intent kotlin.text  Interceptor kotlin.text  LayoutInflater kotlin.text  Locale kotlin.text  LocaleListCompat kotlin.text  Log kotlin.text  
LoginActivity kotlin.text  	LoginInfo kotlin.text  
LoginResponse kotlin.text  Manifest kotlin.text  NewTicketAdapter kotlin.text  NewTicketDetailsActivity kotlin.text  
NewTicketInfo kotlin.text  NewTicketListActivity kotlin.text  NotificationChannel kotlin.text  NotificationCompat kotlin.text  NotificationManager kotlin.text  NotificationManagerCompat kotlin.text  OkHttpClient kotlin.text  PackageManager kotlin.text  
PendingIntent kotlin.text  R kotlin.text  RegisterActivity kotlin.text  RegisterInfo kotlin.text  Retrofit kotlin.text  SERVICE_NOTIFICATION_ID kotlin.text  START_NOT_STICKY kotlin.text  START_STICKY kotlin.text  Settings kotlin.text  
SharedPref kotlin.text  SimpleDateFormat kotlin.text  System kotlin.text  
TicketDone kotlin.text  TicketMonitoring kotlin.text  TicketViewHolder kotlin.text  TimeUnit kotlin.text  TimeZone kotlin.text  Toast kotlin.text  Uri kotlin.text  UserDetails kotlin.text  UserInfoManager kotlin.text  
ViewCompat kotlin.text  Void kotlin.text  Window kotlin.text  WindowInsetsCompat kotlin.text  acceptedTicketAdapter kotlin.text  acceptedTicketList kotlin.text  android kotlin.text  applicationContext kotlin.text  apply kotlin.text  call_new_ticket_list kotlin.text  changeLanguage kotlin.text  dLocale kotlin.text  endsWith kotlin.text  exitProcess kotlin.text  finish kotlin.text  finishAffinity kotlin.text  	getString kotlin.text  getUserData kotlin.text  handler kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  
isNullOrEmpty kotlin.text  java kotlin.text  
lastDetect kotlin.text  	lastVisit kotlin.text  let kotlin.text  listOf kotlin.text  map kotlin.text  mapOf kotlin.text  
mutableListOf kotlin.text  newTicketAdapter kotlin.text  onBackPressedDispatcher kotlin.text  	retrofit2 kotlin.text  
sharedPref kotlin.text  showConfirmDialog kotlin.text  showMessageDialog kotlin.text  showSettingDialog kotlin.text  showTicketNotification kotlin.text  
startActivity kotlin.text  startMonitoringService kotlin.text  
startsWith kotlin.text  to kotlin.text  toInt kotlin.text  toIntOrNull kotlin.text  toSet kotlin.text  toString kotlin.text  toTypedArray kotlin.text  trim kotlin.text  username kotlin.text  Dns okhttp3  Interceptor okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  SYSTEM okhttp3.Dns  SYSTEM okhttp3.Dns.Companion  <SAM-CONSTRUCTOR> okhttp3.Interceptor  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  invoke okhttp3.Interceptor.Companion  Builder okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  connectTimeout okhttp3.OkHttpClient.Builder  dns okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  Builder okhttp3.OkHttpClient.Companion  
newBuilder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  string okhttp3.ResponseBody  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  getAPPLY &okhttp3.logging.HttpLoggingInterceptor  getApply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  invoke 0okhttp3.logging.HttpLoggingInterceptor.Companion  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  NONE ,okhttp3.logging.HttpLoggingInterceptor.Level  Text org.w3c.dom  Call 	retrofit2  Callback 	retrofit2  Response 	retrofit2  Retrofit 	retrofit2  cancel retrofit2.Call  clone retrofit2.Call  enqueue retrofit2.Call  
getISCanceled retrofit2.Call  
getISExecuted retrofit2.Call  
getIsCanceled retrofit2.Call  
getIsExecuted retrofit2.Call  
isCanceled retrofit2.Call  
isExecuted retrofit2.Call  setCanceled retrofit2.Call  setExecuted retrofit2.Call  body retrofit2.Response  code retrofit2.Response  	errorBody retrofit2.Response  getISSuccessful retrofit2.Response  getIsSuccessful retrofit2.Response  isSuccessful retrofit2.Response  
setSuccessful retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  equals retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  Body retrofit2.http  GET retrofit2.http  Headers retrofit2.http  POST retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    