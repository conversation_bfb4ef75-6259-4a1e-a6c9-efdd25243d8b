Tcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketDetailsActivityQcom.example.standardtemplate.Activities.AcceptedTicket.AcceptedTicketListActivityTcom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapterecom.example.standardtemplate.Activities.AcceptedTicket.Adapter.AcceptedTicketAdapter.TicketViewHolderCcom.example.standardtemplate.Activities.Login_Setting.LoginActivityKcom.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter\com.example.standardtemplate.Activities.NewTickets.Adapter.NewTicketAdapter.TicketViewHolderKcom.example.standardtemplate.Activities.NewTickets.NewTicketDetailsActivityHcom.example.standardtemplate.Activities.NewTickets.NewTicketListActivityAcom.example.standardtemplate.Activities.Register.RegisterActivity?com.example.standardtemplate.Libraries.FirebaseMessagingServiceCcom.example.standardtemplate.Libraries.LanguageSetting.BaseActivityGcom.example.standardtemplate.Libraries.LanguageSetting.StandardTemplate:com.example.standardtemplate.Libraries.NotificationService7com.example.standardtemplate.Libraries.TicketMonitoring                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           