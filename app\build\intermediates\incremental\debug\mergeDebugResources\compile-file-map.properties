#Fri Aug 01 10:57:03 MYT 2025
com.example.standardtemplate.app-main-48\:/drawable/application_icon.png=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_application_icon.png.flat
com.example.standardtemplate.app-main-48\:/drawable/dialog_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_dialog_background.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/ic_back.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_back.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/ic_close.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_close.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/ic_launcher_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_background.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/ic_launcher_foreground.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_launcher_foreground.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/ic_noti_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_ic_noti_background.xml.flat
com.example.standardtemplate.app-main-48\:/drawable/input_field_background.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\drawable_input_field_background.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_accepted_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_accepted_ticket_details.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_login.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_login.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_new_ticket_details.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_new_ticket_details.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-48\:/layout/activity_register.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_activity_register.xml.flat
com.example.standardtemplate.app-main-48\:/layout/dialog_confirmation.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_confirmation.xml.flat
com.example.standardtemplate.app-main-48\:/layout/dialog_create.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_create.xml.flat
com.example.standardtemplate.app-main-48\:/layout/dialog_message.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_message.xml.flat
com.example.standardtemplate.app-main-48\:/layout/dialog_setting.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_dialog_setting.xml.flat
com.example.standardtemplate.app-main-48\:/layout/item_accepted_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_item_accepted_ticket_list.xml.flat
com.example.standardtemplate.app-main-48\:/layout/recycle_view_new_ticket_list.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_recycle_view_new_ticket_list.xml.flat
com.example.standardtemplate.app-main-48\:/layout/spinner_item.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\layout_spinner_item.xml.flat
com.example.standardtemplate.app-main-48\:/mipmap-anydpi-v26/ic_launcher.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher.xml.flat
com.example.standardtemplate.app-main-48\:/mipmap-anydpi-v26/ic_launcher_round.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-anydpi-v26_ic_launcher_round.xml.flat
com.example.standardtemplate.app-main-48\:/mipmap-hdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-hdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-hdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-hdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-mdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-mdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-mdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-mdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxxhdpi/ic_launcher.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxxhdpi/ic_launcher_foreground.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat
com.example.standardtemplate.app-main-48\:/mipmap-xxxhdpi/ic_launcher_round.webp=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\mipmap-xxxhdpi_ic_launcher_round.webp.flat
com.example.standardtemplate.app-main-48\:/xml/backup_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_backup_rules.xml.flat
com.example.standardtemplate.app-main-48\:/xml/data_extraction_rules.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_data_extraction_rules.xml.flat
com.example.standardtemplate.app-main-48\:/xml/locales_config.xml=D\:\\MobileProject\\standardtemplate-kotlin\\app\\build\\intermediates\\merged_res\\debug\\mergeDebugResources\\xml_locales_config.xml.flat
